import { ModelInfo } from '../types';
/**
 * Comprehensive model metadata with context lengths and capabilities
 */
export declare const modelInfo: Record<string, ModelInfo>;
/**
 * Get model information by ID
 */
export declare function getModelInfo(modelId: string): ModelInfo | undefined;
/**
 * Get models by provider
 */
export declare function getModelsByProvider(provider: string): ModelInfo[];
/**
 * Get models by capability
 */
export declare function getModelsByCapability(capability: string): ModelInfo[];
/**
 * Get context length for a model
 */
export declare function getModelContextLength(modelId: string): number;
/**
 * Check if model supports a capability
 */
export declare function modelSupportsCapability(modelId: string, capability: string): boolean;
/**
 * Get recommended models for different use cases
 */
export declare const modelRecommendations: {
    coding: string[];
    reasoning: string[];
    vision: string[];
    fast: string[];
    local: string[];
    budget: string[];
    long_context: string[];
};
/**
 * Get models by use case
 */
export declare function getModelsByUseCase(useCase: keyof typeof modelRecommendations): string[];
/**
 * Calculate estimated cost for a request
 */
export declare function estimateCost(modelId: string, inputTokens: number, outputTokens: number): number;
//# sourceMappingURL=model-info.d.ts.map