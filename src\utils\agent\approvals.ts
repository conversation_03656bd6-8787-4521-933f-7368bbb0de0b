import { ApprovalPolicy, ExecInput, PatchOperation } from '../../types';

/**
 * Approval system for managing user consent for agent actions
 */

export interface ApprovalRequest {
  id: string;
  type: 'exec' | 'patch' | 'file_create' | 'file_delete' | 'network';
  description: string;
  details: any;
  timestamp: number;
  approved?: boolean;
  reason?: string;
}

export interface ApprovalResult {
  approved: boolean;
  reason?: string;
  remember?: boolean;
}

/**
 * Check if an action requires approval based on the current policy
 */
export function requiresApproval(
  action: ApprovalRequest,
  policy: ApprovalPolicy
): boolean {
  switch (policy) {
    case 'always':
      return true;
    
    case 'never':
      return false;
    
    case 'dangerous':
      return isDangerousAction(action);
    
    case 'write':
      return isWriteAction(action);
    
    default:
      return true;
  }
}

/**
 * Determine if an action is considered dangerous
 */
function isDangerousAction(action: ApprovalRequest): boolean {
  switch (action.type) {
    case 'exec':
      return isDangerousCommand(action.details as ExecInput);
    
    case 'file_delete':
      return true;
    
    case 'patch':
      return isDangerousPatch(action.details as PatchOperation[]);
    
    case 'network':
      return true;
    
    default:
      return false;
  }
}

/**
 * Determine if an action involves writing/modifying files
 */
function isWriteAction(action: ApprovalRequest): boolean {
  switch (action.type) {
    case 'exec':
      return isWriteCommand(action.details as ExecInput);
    
    case 'patch':
    case 'file_create':
    case 'file_delete':
      return true;
    
    default:
      return false;
  }
}

/**
 * Check if a command is considered dangerous
 */
function isDangerousCommand(input: ExecInput): boolean {
  const command = input.command;
  if (!command || command.length === 0) return false;
  
  const cmd = command[0].toLowerCase();
  const args = command.slice(1).join(' ').toLowerCase();
  
  // Dangerous commands
  const dangerousCommands = [
    'rm', 'del', 'rmdir', 'rd',
    'format', 'fdisk', 'mkfs',
    'dd', 'shred', 'wipe',
    'sudo', 'su', 'runas',
    'chmod', 'chown', 'icacls',
    'systemctl', 'service', 'sc',
    'reboot', 'shutdown', 'halt',
    'kill', 'killall', 'taskkill',
    'crontab', 'at', 'schtasks'
  ];
  
  if (dangerousCommands.includes(cmd)) {
    return true;
  }
  
  // Dangerous patterns
  const dangerousPatterns = [
    /rm\s+.*-rf/,
    /del\s+.*\/s/,
    /format\s+[a-z]:/,
    />\s*\/dev\/(null|zero)/,
    /curl.*\|\s*bash/,
    /wget.*\|\s*sh/,
    /eval\s*\(/,
    /exec\s*\(/
  ];
  
  const fullCommand = command.join(' ').toLowerCase();
  return dangerousPatterns.some(pattern => pattern.test(fullCommand));
}

/**
 * Check if a command involves writing operations
 */
function isWriteCommand(input: ExecInput): boolean {
  const command = input.command;
  if (!command || command.length === 0) return false;
  
  const cmd = command[0].toLowerCase();
  const fullCommand = command.join(' ').toLowerCase();
  
  // Write commands
  const writeCommands = [
    'touch', 'echo', 'cat', 'tee',
    'cp', 'copy', 'mv', 'move', 'rename',
    'mkdir', 'md', 'rmdir', 'rd',
    'rm', 'del', 'unlink',
    'chmod', 'chown', 'icacls',
    'git', 'npm', 'yarn', 'pip',
    'make', 'cmake', 'msbuild'
  ];
  
  if (writeCommands.includes(cmd)) {
    return true;
  }
  
  // Write patterns
  const writePatterns = [
    />\s*[^&|]/,  // Output redirection
    />>/,         // Append redirection
    /\|\s*tee/,   // Tee command
    /--output/,   // Output flag
    /-o\s+/       // Output flag
  ];
  
  return writePatterns.some(pattern => pattern.test(fullCommand));
}

/**
 * Check if patch operations are dangerous
 */
function isDangerousPatch(operations: PatchOperation[]): boolean {
  return operations.some(op => {
    // Deleting files is dangerous
    if (op.type === 'delete') {
      return true;
    }
    
    // Modifying system files is dangerous
    const systemPaths = [
      '/etc/', '/usr/bin/', '/usr/sbin/', '/bin/', '/sbin/',
      'C:\\Windows\\', 'C:\\Program Files\\', 'C:\\System32\\'
    ];
    
    return systemPaths.some(path => op.filePath.startsWith(path));
  });
}

/**
 * Create an approval request for command execution
 */
export function createExecApprovalRequest(
  input: ExecInput,
  description?: string
): ApprovalRequest {
  return {
    id: generateApprovalId(),
    type: 'exec',
    description: description || `Execute command: ${input.command.join(' ')}`,
    details: input,
    timestamp: Date.now()
  };
}

/**
 * Create an approval request for file patching
 */
export function createPatchApprovalRequest(
  operations: PatchOperation[],
  description?: string
): ApprovalRequest {
  const fileCount = operations.length;
  const types = [...new Set(operations.map(op => op.type))];
  
  return {
    id: generateApprovalId(),
    type: 'patch',
    description: description || `Apply ${fileCount} file changes (${types.join(', ')})`,
    details: operations,
    timestamp: Date.now()
  };
}

/**
 * Create an approval request for file creation
 */
export function createFileCreateApprovalRequest(
  filePath: string,
  description?: string
): ApprovalRequest {
  return {
    id: generateApprovalId(),
    type: 'file_create',
    description: description || `Create file: ${filePath}`,
    details: { filePath },
    timestamp: Date.now()
  };
}

/**
 * Create an approval request for file deletion
 */
export function createFileDeleteApprovalRequest(
  filePath: string,
  description?: string
): ApprovalRequest {
  return {
    id: generateApprovalId(),
    type: 'file_delete',
    description: description || `Delete file: ${filePath}`,
    details: { filePath },
    timestamp: Date.now()
  };
}

/**
 * Create an approval request for network operations
 */
export function createNetworkApprovalRequest(
  url: string,
  method: string = 'GET',
  description?: string
): ApprovalRequest {
  return {
    id: generateApprovalId(),
    type: 'network',
    description: description || `${method} request to: ${url}`,
    details: { url, method },
    timestamp: Date.now()
  };
}

/**
 * Generate a unique approval ID
 */
function generateApprovalId(): string {
  return `approval_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Format approval request for display
 */
export function formatApprovalRequest(request: ApprovalRequest): string {
  const timestamp = new Date(request.timestamp).toLocaleTimeString();
  let details = '';
  
  switch (request.type) {
    case 'exec':
      const execDetails = request.details as ExecInput;
      details = `Command: ${execDetails.command.join(' ')}`;
      if (execDetails.workdir) {
        details += `\nWorking Directory: ${execDetails.workdir}`;
      }
      break;
    
    case 'patch':
      const patchDetails = request.details as PatchOperation[];
      details = `Files to modify: ${patchDetails.length}`;
      patchDetails.forEach((op, i) => {
        details += `\n  ${i + 1}. ${op.type.toUpperCase()}: ${op.filePath}`;
      });
      break;
    
    case 'file_create':
    case 'file_delete':
      details = `File: ${request.details.filePath}`;
      break;
    
    case 'network':
      details = `URL: ${request.details.url}\nMethod: ${request.details.method}`;
      break;
  }
  
  return `[${timestamp}] ${request.description}\n${details}`;
}

/**
 * Get risk level for an approval request
 */
export function getRiskLevel(request: ApprovalRequest): 'low' | 'medium' | 'high' {
  if (isDangerousAction(request)) {
    return 'high';
  }
  
  if (isWriteAction(request)) {
    return 'medium';
  }
  
  return 'low';
}

/**
 * Get recommended action for an approval request
 */
export function getRecommendedAction(request: ApprovalRequest): 'approve' | 'reject' | 'review' {
  const riskLevel = getRiskLevel(request);
  
  switch (riskLevel) {
    case 'high':
      return 'reject';
    case 'medium':
      return 'review';
    case 'low':
    default:
      return 'approve';
  }
}
