#!/bin/bash

# Kritrima AI CLI Deployment Script
# This script helps deploy the Kritrima AI CLI for production use

set -e

echo "🚀 Kritrima AI CLI Deployment Script"
echo "===================================="

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2)
REQUIRED_VERSION="18.0.0"

if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
    echo "❌ Node.js version $NODE_VERSION is too old. Please upgrade to Node.js 18+."
    exit 1
fi

echo "✅ Node.js version $NODE_VERSION detected"

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Run core functionality test
echo "🧪 Testing core functionality..."
node test-core.js

# Create configuration directory
CONFIG_DIR="$HOME/.kritrima-ai"
if [ ! -d "$CONFIG_DIR" ]; then
    echo "📁 Creating configuration directory: $CONFIG_DIR"
    mkdir -p "$CONFIG_DIR"
fi

# Create example configuration
CONFIG_FILE="$CONFIG_DIR/config.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "⚙️ Creating example configuration..."
    cat > "$CONFIG_FILE" << EOF
# Kritrima AI CLI Configuration
model: gpt-4
provider: openai
approvalMode: dangerous
maxTokens: 4096
temperature: 0.7
timeout: 30000
workdir: $(pwd)
EOF
    echo "✅ Configuration created at: $CONFIG_FILE"
fi

# Create environment file template
ENV_FILE=".env"
if [ ! -f "$ENV_FILE" ]; then
    echo "🔑 Creating environment file template..."
    cat > "$ENV_FILE" << EOF
# Kritrima AI CLI Environment Variables
# Add your API keys here

# OpenAI
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google
GOOGLE_API_KEY=your_google_api_key_here

# Mistral
MISTRAL_API_KEY=your_mistral_api_key_here

# Custom providers
# CUSTOM_PROVIDER_URL=https://api.example.com
# CUSTOM_PROVIDER_KEY=your_custom_key_here
EOF
    echo "✅ Environment template created at: $ENV_FILE"
    echo "⚠️  Please edit $ENV_FILE and add your API keys"
fi

# Create startup script
STARTUP_SCRIPT="kritrima-ai"
echo "📝 Creating startup script..."
cat > "$STARTUP_SCRIPT" << 'EOF'
#!/bin/bash
# Kritrima AI CLI Startup Script

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Load environment variables
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Run the CLI
npx tsx src/cli.tsx "$@"
EOF

chmod +x "$STARTUP_SCRIPT"
echo "✅ Startup script created: ./$STARTUP_SCRIPT"

# Create global symlink (optional)
read -p "🔗 Create global symlink? This will allow you to run 'kritrima-ai' from anywhere (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    GLOBAL_LINK="/usr/local/bin/kritrima-ai"
    if [ -w "/usr/local/bin" ]; then
        ln -sf "$(pwd)/$STARTUP_SCRIPT" "$GLOBAL_LINK"
        echo "✅ Global symlink created: $GLOBAL_LINK"
    else
        echo "⚠️  Creating global symlink requires sudo:"
        sudo ln -sf "$(pwd)/$STARTUP_SCRIPT" "$GLOBAL_LINK"
        echo "✅ Global symlink created: $GLOBAL_LINK"
    fi
fi

# Test the installation
echo ""
echo "🧪 Testing installation..."
if ./"$STARTUP_SCRIPT" --version &>/dev/null; then
    echo "✅ Installation test passed"
else
    echo "⚠️  Installation test failed - you may need to configure API keys"
fi

echo ""
echo "🎉 Deployment Complete!"
echo "======================"
echo ""
echo "📋 Next Steps:"
echo "1. Edit $ENV_FILE and add your API keys"
echo "2. Run: ./$STARTUP_SCRIPT --help"
echo "3. Start chatting: ./$STARTUP_SCRIPT"
echo ""
echo "📖 Documentation:"
echo "- README.md for detailed usage instructions"
echo "- plan.md for architecture overview"
echo ""
echo "🔧 Troubleshooting:"
echo "- If you see module resolution errors, use: npx tsx src/cli.tsx"
echo "- For build issues, see README.md for alternative deployment methods"
echo ""
echo "✨ Features Available:"
echo "- Multi-provider AI support (OpenAI, Anthropic, Google, Mistral)"
echo "- Autonomous agent with tool calling"
echo "- Secure sandbox execution"
echo "- File patching and modification"
echo "- Session management"
echo "- Interactive terminal UI"
echo "- Approval workflows"
echo ""
echo "🚀 Ready for production use!"
