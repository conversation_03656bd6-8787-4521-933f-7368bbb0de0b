"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalChatResponseItem = TerminalChatResponseItem;
const jsx_runtime_1 = require("react/jsx-runtime");
const ink_1 = require("ink");
function TerminalChatResponseItem({ item }) {
    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleTimeString();
    };
    const renderContent = () => {
        return item.content.map((content, index) => {
            switch (content.type) {
                case 'text':
                    return ((0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", children: (0, jsx_runtime_1.jsx)(ink_1.Text, { children: content.text }) }, index));
                case 'function_call':
                    if (content.function_call) {
                        return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginY: 1, paddingX: 2, borderStyle: "round", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "blue", bold: true, children: ["\uD83D\uDD27 Function Call: ", content.function_call.name] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", children: ["Arguments: ", content.function_call.arguments] })] }, index));
                    }
                    break;
                case 'function_result':
                    if (content.function_result) {
                        return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginY: 1, paddingX: 2, borderStyle: "round", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: content.function_result.success ? "green" : "red", bold: true, children: [content.function_result.success ? "✅" : "❌", " Function Result"] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { children: content.function_result.result }), content.function_result.metadata && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", dimColor: true, children: ["Metadata: ", JSON.stringify(content.function_result.metadata, null, 2)] }))] }, index));
                    }
                    break;
                default:
                    return null;
            }
        });
    };
    const getRoleIcon = () => {
        switch (item.role) {
            case 'user':
                return '👤';
            case 'assistant':
                return '🤖';
            case 'system':
                return '⚙️';
            case 'tool':
                return '🔧';
            default:
                return '❓';
        }
    };
    const getRoleColor = () => {
        switch (item.role) {
            case 'user':
                return 'cyan';
            case 'assistant':
                return 'green';
            case 'system':
                return 'yellow';
            case 'tool':
                return 'blue';
            default:
                return 'gray';
        }
    };
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginY: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Box, { marginBottom: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: getRoleColor(), bold: true, children: [getRoleIcon(), " ", item.role.charAt(0).toUpperCase() + item.role.slice(1)] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, marginLeft: 2, children: formatTimestamp(item.timestamp) }), item.type === 'error' && ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: "red", marginLeft: 2, children: "[ERROR]" }))] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", paddingLeft: 2, children: renderContent() }), item.metadata && Object.keys(item.metadata).length > 0 && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, paddingLeft: 2, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", dimColor: true, children: [item.metadata.command && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { children: ["Command: ", item.metadata.command, " "] })), item.metadata.exitCode !== undefined && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { children: ["Exit: ", item.metadata.exitCode, " "] })), item.metadata.duration && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { children: ["Duration: ", item.metadata.duration, "ms "] })), item.metadata.success !== undefined && ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: item.metadata.success ? "green" : "red", children: item.metadata.success ? "✓" : "✗" }))] }) }))] }));
}
//# sourceMappingURL=terminal-chat-response-item.js.map