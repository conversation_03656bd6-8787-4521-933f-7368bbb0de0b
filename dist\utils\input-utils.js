"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createInputItem = createInputItem;
exports.processImage = processImage;
exports.validateImageFile = validateImageFile;
exports.extractFilePaths = extractFilePaths;
exports.getFileSystemSuggestions = getFileSystemSuggestions;
exports.formatFileSize = formatFileSize;
exports.isImageFile = isImageFile;
exports.getSupportedImageExtensions = getSupportedImageExtensions;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const mimeTypes = __importStar(require("mime-types"));
const types_1 = require("../types");
const SUPPORTED_IMAGE_TYPES = ['image/png', 'image/jpeg', 'image/webp', 'image/gif'];
const MAX_IMAGE_SIZE = 20 * 1024 * 1024; // 20MB
const MAX_IMAGE_DIMENSION = 2048;
/**
 * Create input item from text and image paths
 */
async function createInputItem(text, imagePaths = []) {
    const content = [];
    // Add text content if provided
    if (text.trim()) {
        content.push({
            type: "input_text",
            text: text.trim()
        });
    }
    // Process image paths
    for (const imagePath of imagePaths) {
        try {
            const imageData = await processImage(imagePath);
            content.push({
                type: "input_image",
                image: imageData
            });
        }
        catch (error) {
            console.warn(`Failed to process image ${imagePath}:`, error);
            // Continue with other images instead of failing completely
        }
    }
    return {
        role: "user",
        content,
        type: "message",
        timestamp: Date.now()
    };
}
/**
 * Process an image file for API consumption
 */
async function processImage(imagePath) {
    // Validate file exists
    if (!fs.existsSync(imagePath)) {
        throw new types_1.ValidationError(`Image file not found: ${imagePath}`);
    }
    // Get file stats
    const stats = fs.statSync(imagePath);
    if (stats.size > MAX_IMAGE_SIZE) {
        throw new types_1.ValidationError(`Image file too large: ${stats.size} bytes (max: ${MAX_IMAGE_SIZE})`);
    }
    // Determine MIME type
    const mimeType = mimeTypes.lookup(imagePath);
    if (!mimeType || !SUPPORTED_IMAGE_TYPES.includes(mimeType)) {
        throw new types_1.ValidationError(`Unsupported image type: ${mimeType}. Supported types: ${SUPPORTED_IMAGE_TYPES.join(', ')}`);
    }
    // Read and encode image
    const imageBuffer = fs.readFileSync(imagePath);
    const base64Data = imageBuffer.toString('base64');
    // Get image dimensions (basic validation)
    const dimensions = await getImageDimensions(imageBuffer, mimeType);
    return {
        base64: base64Data,
        mimeType,
        width: dimensions.width,
        height: dimensions.height
    };
}
/**
 * Get image dimensions from buffer
 */
async function getImageDimensions(buffer, mimeType) {
    try {
        // Basic dimension extraction for common formats
        if (mimeType === 'image/png') {
            return extractPngDimensions(buffer);
        }
        else if (mimeType === 'image/jpeg') {
            return extractJpegDimensions(buffer);
        }
        else {
            // For other formats, return default dimensions
            return { width: 0, height: 0 };
        }
    }
    catch (error) {
        console.warn('Failed to extract image dimensions:', error);
        return { width: 0, height: 0 };
    }
}
/**
 * Extract PNG dimensions from buffer
 */
function extractPngDimensions(buffer) {
    // PNG signature: 89 50 4E 47 0D 0A 1A 0A
    if (buffer.length < 24) {
        throw new Error('Invalid PNG file');
    }
    // Check PNG signature
    const signature = buffer.subarray(0, 8);
    const expectedSignature = Buffer.from([0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]);
    if (!signature.equals(expectedSignature)) {
        throw new Error('Invalid PNG signature');
    }
    // Read IHDR chunk (starts at byte 8)
    const width = buffer.readUInt32BE(16);
    const height = buffer.readUInt32BE(20);
    return { width, height };
}
/**
 * Extract JPEG dimensions from buffer
 */
function extractJpegDimensions(buffer) {
    // JPEG starts with FF D8
    if (buffer.length < 4 || buffer[0] !== 0xFF || buffer[1] !== 0xD8) {
        throw new Error('Invalid JPEG file');
    }
    let offset = 2;
    while (offset < buffer.length) {
        // Find next marker
        if (buffer[offset] !== 0xFF) {
            offset++;
            continue;
        }
        const marker = buffer[offset + 1];
        // SOF markers (Start of Frame)
        if ((marker >= 0xC0 && marker <= 0xC3) ||
            (marker >= 0xC5 && marker <= 0xC7) ||
            (marker >= 0xC9 && marker <= 0xCB) ||
            (marker >= 0xCD && marker <= 0xCF)) {
            if (offset + 9 >= buffer.length)
                break;
            const height = buffer.readUInt16BE(offset + 5);
            const width = buffer.readUInt16BE(offset + 7);
            return { width, height };
        }
        // Skip to next segment
        if (offset + 3 >= buffer.length)
            break;
        const segmentLength = buffer.readUInt16BE(offset + 2);
        offset += 2 + segmentLength;
    }
    throw new Error('Could not find JPEG dimensions');
}
/**
 * Validate image file before processing
 */
function validateImageFile(imagePath) {
    try {
        // Check if file exists
        if (!fs.existsSync(imagePath)) {
            return { valid: false, error: 'File not found' };
        }
        // Check file size
        const stats = fs.statSync(imagePath);
        if (stats.size > MAX_IMAGE_SIZE) {
            return { valid: false, error: `File too large (${stats.size} bytes, max: ${MAX_IMAGE_SIZE})` };
        }
        // Check MIME type
        const mimeType = mimeTypes.lookup(imagePath);
        if (!mimeType || !SUPPORTED_IMAGE_TYPES.includes(mimeType)) {
            return { valid: false, error: `Unsupported file type: ${mimeType}` };
        }
        return { valid: true };
    }
    catch (error) {
        return { valid: false, error: `Validation error: ${error}` };
    }
}
/**
 * Extract file paths from text using @ prefix
 */
function extractFilePaths(text) {
    const filePathRegex = /@([^\s]+)/g;
    const filePaths = [];
    let match;
    while ((match = filePathRegex.exec(text)) !== null) {
        const filePath = match[1];
        // Resolve relative paths
        const resolvedPath = path.isAbsolute(filePath) ? filePath : path.resolve(filePath);
        filePaths.push(resolvedPath);
    }
    // Remove file paths from text
    const cleanText = text.replace(filePathRegex, '').trim();
    return { cleanText, filePaths };
}
/**
 * Get file system suggestions for autocomplete
 */
function getFileSystemSuggestions(input, workdir = process.cwd(), maxSuggestions = 10) {
    try {
        // Extract the path part after @
        const atIndex = input.lastIndexOf('@');
        if (atIndex === -1)
            return [];
        const pathPart = input.substring(atIndex + 1);
        const basePath = path.dirname(pathPart);
        const fileName = path.basename(pathPart);
        // Determine search directory
        const searchDir = path.isAbsolute(basePath)
            ? basePath
            : path.resolve(workdir, basePath);
        if (!fs.existsSync(searchDir)) {
            return [];
        }
        // Read directory contents
        const entries = fs.readdirSync(searchDir, { withFileTypes: true });
        return entries
            .filter(entry => {
            // Filter by filename prefix
            return entry.name.toLowerCase().startsWith(fileName.toLowerCase());
        })
            .slice(0, maxSuggestions)
            .map(entry => {
            const fullPath = path.join(searchDir, entry.name);
            const relativePath = path.relative(workdir, fullPath);
            return {
                path: relativePath,
                type: entry.isDirectory() ? 'directory' : 'file',
                exists: true
            };
        })
            .sort((a, b) => {
            // Sort directories first, then files
            if (a.type !== b.type) {
                return a.type === 'directory' ? -1 : 1;
            }
            return a.path.localeCompare(b.path);
        });
    }
    catch (error) {
        console.warn('Error getting file system suggestions:', error);
        return [];
    }
}
/**
 * Format file size for display
 */
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    return `${size.toFixed(1)} ${units[unitIndex]}`;
}
/**
 * Check if file is an image
 */
function isImageFile(filePath) {
    const mimeType = mimeTypes.lookup(filePath);
    return mimeType ? SUPPORTED_IMAGE_TYPES.includes(mimeType) : false;
}
/**
 * Get supported image extensions
 */
function getSupportedImageExtensions() {
    return SUPPORTED_IMAGE_TYPES.map(type => {
        const ext = mimeTypes.extension(type);
        return ext ? `.${ext}` : '';
    }).filter(Boolean);
}
//# sourceMappingURL=input-utils.js.map