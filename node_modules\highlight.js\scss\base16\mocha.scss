pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Mocha
  Author: <PERSON> (http://chriskempson.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme mocha
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #3B3228  Default Background
base01  #534636  Lighter Background (Used for status bars, line number and folding marks)
base02  #645240  Selection Background
base03  #7e705a  Comments, Invisibles, Line Highlighting
base04  #b8afad  Dark Foreground (Used for status bars)
base05  #d0c8c6  Default Foreground, Caret, Delimiters, Operators
base06  #e9e1dd  Light Foreground (Not often used)
base07  #f5eeeb  Light Background (Not often used)
base08  #cb6077  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #d28b71  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f4bc87  Classes, Markup Bold, Search Text Background
base0B  #beb55b  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #7bbda4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #8ab3b5  Functions, Methods, Attribute IDs, Headings
base0E  #a89bb9  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #bb9584  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #d0c8c6;
  background: #3B3228
}
.hljs::selection,
.hljs ::selection {
  background-color: #645240;
  color: #d0c8c6
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #7e705a -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #7e705a
}
/* base04 - #b8afad -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #b8afad
}
/* base05 - #d0c8c6 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #d0c8c6
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #cb6077
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #d28b71
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f4bc87
}
.hljs-strong {
  font-weight: bold;
  color: #f4bc87
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #beb55b
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #7bbda4
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #8ab3b5
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #a89bb9
}
.hljs-emphasis {
  color: #a89bb9;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #bb9584
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}