"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.macOSSeatbelt = exports.landlock = exports.rawExec = void 0;
exports.getBestSandbox = getBestSandbox;
exports.execSandboxed = execSandboxed;
exports.getSandboxCapabilities = getSandboxCapabilities;
exports.isSandboxAvailable = isSandboxAvailable;
exports.getAvailableSandboxes = getAvailableSandboxes;
exports.testSandbox = testSandbox;
exports.getSandboxRecommendation = getSandboxRecommendation;
const os = __importStar(require("os"));
const rawExec = __importStar(require("./raw-exec"));
exports.rawExec = rawExec;
// Platform-specific sandbox implementations will be imported conditionally
let landlockExec = null;
let macOSSeatbeltExec = null;
// Try to import platform-specific modules
try {
    if (os.platform() === 'linux') {
        landlockExec = require('./landlock');
    }
}
catch (error) {
    // Landlock not available
}
try {
    if (os.platform() === 'darwin') {
        macOSSeatbeltExec = require('./macos-seatbelt');
    }
}
catch (error) {
    // macOS Seatbelt not available
}
/**
 * Get the best available sandbox implementation for the current platform
 */
function getBestSandbox() {
    const platform = os.platform();
    // Try platform-specific sandboxes first
    if (platform === 'linux' && landlockExec?.isAvailable()) {
        return {
            exec: landlockExec.execWithLandlock,
            capabilities: {
                sandboxed: true,
                networkRestricted: true,
                filesystemRestricted: true,
                processRestricted: true,
                name: 'Linux Landlock',
                securityLevel: 'High - Kernel-level sandboxing'
            },
            available: true
        };
    }
    if (platform === 'darwin' && macOSSeatbeltExec?.isAvailable()) {
        return {
            exec: macOSSeatbeltExec.execWithSeatbelt,
            capabilities: {
                sandboxed: true,
                networkRestricted: true,
                filesystemRestricted: true,
                processRestricted: true,
                name: 'macOS Seatbelt',
                securityLevel: 'High - System-level sandboxing'
            },
            available: true
        };
    }
    // Fallback to raw execution with basic security
    return {
        exec: rawExec.execWithBasicSecurity,
        capabilities: {
            sandboxed: false,
            networkRestricted: false,
            filesystemRestricted: false,
            processRestricted: false,
            name: 'Raw Execution',
            securityLevel: 'Basic - Security checks only'
        },
        available: true
    };
}
/**
 * Execute command with the best available sandbox
 */
async function execSandboxed(input, config, additionalWritableRoots = [], signal) {
    const sandbox = getBestSandbox();
    return sandbox.exec(input, config, additionalWritableRoots, signal);
}
/**
 * Get current sandbox capabilities
 */
function getSandboxCapabilities() {
    return getBestSandbox().capabilities;
}
/**
 * Check if any sandbox is available
 */
function isSandboxAvailable() {
    return getBestSandbox().available;
}
/**
 * Get all available sandbox implementations
 */
function getAvailableSandboxes() {
    const sandboxes = [];
    // Linux Landlock
    sandboxes.push({
        name: 'Linux Landlock',
        platform: 'linux',
        available: os.platform() === 'linux' && landlockExec?.isAvailable(),
        capabilities: {
            sandboxed: true,
            networkRestricted: true,
            filesystemRestricted: true,
            processRestricted: true,
            name: 'Linux Landlock',
            securityLevel: 'High - Kernel-level sandboxing'
        }
    });
    // macOS Seatbelt
    sandboxes.push({
        name: 'macOS Seatbelt',
        platform: 'darwin',
        available: os.platform() === 'darwin' && macOSSeatbeltExec?.isAvailable(),
        capabilities: {
            sandboxed: true,
            networkRestricted: true,
            filesystemRestricted: true,
            processRestricted: true,
            name: 'macOS Seatbelt',
            securityLevel: 'High - System-level sandboxing'
        }
    });
    // Raw execution (always available)
    sandboxes.push({
        name: 'Raw Execution',
        platform: 'all',
        available: true,
        capabilities: {
            sandboxed: false,
            networkRestricted: false,
            filesystemRestricted: false,
            processRestricted: false,
            name: 'Raw Execution',
            securityLevel: 'Basic - Security checks only'
        }
    });
    return sandboxes;
}
/**
 * Test sandbox functionality
 */
async function testSandbox(config) {
    const sandbox = getBestSandbox();
    const testResults = [];
    try {
        // Test 1: Basic command execution
        const echoTest = await sandbox.exec({ command: ['echo', 'test'] }, config);
        testResults.push({
            test: 'Basic command execution',
            passed: echoTest.success && echoTest.stdout.includes('test'),
            details: echoTest.success ? 'OK' : echoTest.stderr
        });
        // Test 2: Working directory
        const pwdTest = await sandbox.exec({ command: os.platform() === 'win32' ? ['cd'] : ['pwd'] }, config);
        testResults.push({
            test: 'Working directory access',
            passed: pwdTest.success,
            details: pwdTest.success ? 'OK' : pwdTest.stderr
        });
        // Test 3: File listing
        const listTest = await sandbox.exec({ command: os.platform() === 'win32' ? ['dir'] : ['ls'] }, config);
        testResults.push({
            test: 'File system access',
            passed: listTest.success,
            details: listTest.success ? 'OK' : listTest.stderr
        });
        const allPassed = testResults.every(result => result.passed);
        return {
            success: allPassed,
            sandbox: sandbox.capabilities.name,
            capabilities: sandbox.capabilities,
            testResults
        };
    }
    catch (error) {
        testResults.push({
            test: 'Sandbox initialization',
            passed: false,
            details: error instanceof Error ? error.message : 'Unknown error'
        });
        return {
            success: false,
            sandbox: sandbox.capabilities.name,
            capabilities: sandbox.capabilities,
            testResults
        };
    }
}
/**
 * Get sandbox recommendation based on security requirements
 */
function getSandboxRecommendation(securityLevel) {
    const available = getAvailableSandboxes().filter(s => s.available);
    switch (securityLevel) {
        case 'high':
            const highSecurity = available.find(s => s.capabilities.sandboxed);
            if (highSecurity) {
                return {
                    recommended: highSecurity.name,
                    reason: 'Provides kernel/system-level sandboxing for maximum security',
                    alternatives: available.filter(s => s.name !== highSecurity.name).map(s => s.name)
                };
            }
            break;
        case 'medium':
            // Prefer sandboxed but accept basic security
            const mediumSecurity = available.find(s => s.capabilities.sandboxed) || available[0];
            return {
                recommended: mediumSecurity.name,
                reason: mediumSecurity.capabilities.sandboxed
                    ? 'Provides good security with sandboxing'
                    : 'Basic security checks (sandboxing not available)',
                alternatives: available.filter(s => s.name !== mediumSecurity.name).map(s => s.name)
            };
        case 'low':
        default:
            return {
                recommended: 'Raw Execution',
                reason: 'Minimal overhead with basic security checks',
                alternatives: available.filter(s => s.name !== 'Raw Execution').map(s => s.name)
            };
    }
    // Fallback
    return {
        recommended: 'Raw Execution',
        reason: 'Only available option',
        alternatives: []
    };
}
exports.landlock = landlockExec;
exports.macOSSeatbelt = macOSSeatbeltExec;
//# sourceMappingURL=index.js.map