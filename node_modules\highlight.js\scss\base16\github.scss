pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Github
  Author: Defman21
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme github
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #ffffff  Default Background
base01  #f5f5f5  Lighter Background (Used for status bars, line number and folding marks)
base02  #c8c8fa  Selection Background
base03  #969896  Comments, Invisibles, Line Highlighting
base04  #e8e8e8  Dark Foreground (Used for status bars)
base05  #333333  Default Foreground, Caret, Delimiters, Operators
base06  #ffffff  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #ed6a43  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #0086b3  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #795da3  Classes, Markup Bold, Search Text Background
base0B  #183691  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #183691  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #795da3  Functions, Methods, Attribute IDs, Headings
base0E  #a71d5d  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #333333  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #333333;
  background: #ffffff
}
.hljs::selection,
.hljs ::selection {
  background-color: #c8c8fa;
  color: #333333
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #969896 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #969896
}
/* base04 - #e8e8e8 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #e8e8e8
}
/* base05 - #333333 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #333333
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ed6a43
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #0086b3
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #795da3
}
.hljs-strong {
  font-weight: bold;
  color: #795da3
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #183691
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #183691
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #795da3
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #a71d5d
}
.hljs-emphasis {
  color: #a71d5d;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #333333
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}