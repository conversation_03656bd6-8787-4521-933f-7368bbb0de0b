"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.providerRecommendations = exports.providers = void 0;
exports.getProvider = getProvider;
exports.getProviderNames = getProviderNames;
exports.isProviderSupported = isProviderSupported;
exports.getDefaultModel = getDefaultModel;
exports.getProviderModels = getProviderModels;
exports.validateProvider = validateProvider;
exports.getProviderDisplayName = getProviderDisplayName;
exports.providerSupportsModel = providerSupportsModel;
exports.getProvidersByCapability = getProvidersByCapability;
/**
 * Built-in AI provider configurations
 * Each provider has a name, base URL, and environment variable key for API authentication
 */
exports.providers = {
    openai: {
        name: "OpenAI",
        baseURL: "https://api.openai.com/v1",
        envKey: "OPENAI_API_KEY",
        models: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini"],
        defaultModel: "gpt-4"
    },
    azure: {
        name: "Azure OpenAI",
        baseURL: "https://your-resource.openai.azure.com",
        envKey: "AZURE_OPENAI_API_KEY",
        models: ["gpt-4", "gpt-35-turbo"],
        defaultModel: "gpt-4"
    },
    gemini: {
        name: "Google Gemini",
        baseURL: "https://generativelanguage.googleapis.com/v1beta",
        envKey: "GEMINI_API_KEY",
        models: ["gemini-pro", "gemini-pro-vision"],
        defaultModel: "gemini-pro"
    },
    ollama: {
        name: "Ollama",
        baseURL: "http://localhost:11434/v1",
        envKey: "OLLAMA_API_KEY",
        models: ["llama2", "codellama", "mistral", "neural-chat"],
        defaultModel: "llama2"
    },
    mistral: {
        name: "Mistral AI",
        baseURL: "https://api.mistral.ai/v1",
        envKey: "MISTRAL_API_KEY",
        models: ["mistral-tiny", "mistral-small", "mistral-medium", "mistral-large"],
        defaultModel: "mistral-small"
    },
    deepseek: {
        name: "DeepSeek",
        baseURL: "https://api.deepseek.com/v1",
        envKey: "DEEPSEEK_API_KEY",
        models: ["deepseek-chat", "deepseek-coder"],
        defaultModel: "deepseek-chat"
    },
    xai: {
        name: "xAI",
        baseURL: "https://api.x.ai/v1",
        envKey: "XAI_API_KEY",
        models: ["grok-beta"],
        defaultModel: "grok-beta"
    },
    groq: {
        name: "Groq",
        baseURL: "https://api.groq.com/openai/v1",
        envKey: "GROQ_API_KEY",
        models: ["mixtral-8x7b-32768", "llama2-70b-4096"],
        defaultModel: "mixtral-8x7b-32768"
    },
    arceeai: {
        name: "ArceeAI",
        baseURL: "https://api.arcee.ai/v1",
        envKey: "ARCEEAI_API_KEY",
        models: ["arcee-nova", "arcee-spark"],
        defaultModel: "arcee-nova"
    },
    openrouter: {
        name: "OpenRouter",
        baseURL: "https://openrouter.ai/api/v1",
        envKey: "OPENROUTER_API_KEY",
        models: [
            "openai/gpt-4",
            "anthropic/claude-3-opus",
            "meta-llama/llama-2-70b-chat",
            "mistralai/mixtral-8x7b-instruct"
        ],
        defaultModel: "openai/gpt-4"
    }
};
/**
 * Get provider configuration by name
 */
function getProvider(name) {
    return exports.providers[name.toLowerCase()];
}
/**
 * Get all available provider names
 */
function getProviderNames() {
    return Object.keys(exports.providers);
}
/**
 * Check if a provider is supported
 */
function isProviderSupported(name) {
    return name.toLowerCase() in exports.providers;
}
/**
 * Get default model for a provider
 */
function getDefaultModel(provider) {
    const providerConfig = getProvider(provider);
    return providerConfig?.defaultModel || "gpt-4";
}
/**
 * Get available models for a provider
 */
function getProviderModels(provider) {
    const providerConfig = getProvider(provider);
    return providerConfig?.models || [];
}
/**
 * Validate provider configuration
 */
function validateProvider(provider) {
    const config = getProvider(provider);
    if (!config)
        return false;
    return !!(config.name && config.baseURL && config.envKey);
}
/**
 * Get provider display name
 */
function getProviderDisplayName(provider) {
    const config = getProvider(provider);
    return config?.name || provider;
}
/**
 * Check if provider supports a specific model
 */
function providerSupportsModel(provider, model) {
    const models = getProviderModels(provider);
    return models.length === 0 || models.includes(model);
}
/**
 * Get recommended providers for different use cases
 */
exports.providerRecommendations = {
    coding: ["openai", "deepseek", "mistral"],
    general: ["openai", "gemini", "mistral"],
    local: ["ollama"],
    fast: ["groq", "openai"],
    multimodal: ["openai", "gemini"]
};
/**
 * Get providers by capability
 */
function getProvidersByCapability(capability) {
    return exports.providerRecommendations[capability] || [];
}
//# sourceMappingURL=providers.js.map