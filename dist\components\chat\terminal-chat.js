"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalChat = TerminalChat;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const ink_1 = require("ink");
const terminal_chat_input_1 = require("./terminal-chat-input");
const terminal_chat_response_item_1 = require("./terminal-chat-response-item");
const model_overlay_1 = require("../overlays/model-overlay");
const help_overlay_1 = require("../overlays/help-overlay");
const history_overlay_1 = require("../overlays/history-overlay");
const approval_mode_overlay_1 = require("../overlays/approval-mode-overlay");
const agent_loop_1 = require("../../utils/agent/agent-loop");
const app_1 = require("../../app");
function TerminalChat({ config, initialMessage, onExit }) {
    const [model, setModel] = (0, react_1.useState)(config.model);
    const [provider, setProvider] = (0, react_1.useState)(config.provider);
    const [items, setItems] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [approvalPolicy, setApprovalPolicy] = (0, react_1.useState)(config.approvalMode);
    const [overlayMode, setOverlayMode] = (0, react_1.useState)("none");
    const [error, setError] = (0, react_1.useState)(null);
    const [showWelcome, setShowWelcome] = (0, react_1.useState)(!initialMessage);
    const agentLoopRef = (0, react_1.useRef)(null);
    const abortControllerRef = (0, react_1.useRef)(null);
    // Initialize agent loop
    (0, react_1.useEffect)(() => {
        const currentConfig = {
            ...config,
            model,
            provider,
            approvalMode: approvalPolicy
        };
        agentLoopRef.current = new agent_loop_1.AgentLoop(currentConfig);
    }, [config, model, provider, approvalPolicy]);
    // Handle initial message
    (0, react_1.useEffect)(() => {
        if (initialMessage && agentLoopRef.current) {
            handleSendMessage(initialMessage);
            setShowWelcome(false);
        }
    }, [initialMessage]);
    // Handle keyboard shortcuts
    (0, ink_1.useInput)((input, key) => {
        if (key.ctrl && input === 'c') {
            handleExit();
        }
        else if (key.escape) {
            setOverlayMode("none");
        }
        else if (key.ctrl && input === 'm') {
            setOverlayMode("model");
        }
        else if (key.ctrl && input === 'h') {
            setOverlayMode("help");
        }
        else if (key.ctrl && input === 'r') {
            setOverlayMode("history");
        }
    });
    const handleSendMessage = (0, react_1.useCallback)(async (message) => {
        if (!agentLoopRef.current || loading)
            return;
        // Handle slash commands
        if (message.startsWith('/')) {
            handleSlashCommand(message);
            return;
        }
        setLoading(true);
        setError(null);
        setShowWelcome(false);
        try {
            // Create abort controller for this request
            abortControllerRef.current = new AbortController();
            // Add user message to items
            const userItem = {
                id: `user-${Date.now()}`,
                type: 'message',
                role: 'user',
                content: [{ type: 'text', text: message }],
                timestamp: Date.now()
            };
            setItems(prev => [...prev, userItem]);
            // Process with agent loop
            const response = await agentLoopRef.current.processMessage(message, items, {
                onProgress: (partialResponse) => {
                    // Update items with streaming response
                    setItems(prev => {
                        const newItems = [...prev];
                        const lastItem = newItems[newItems.length - 1];
                        if (lastItem && lastItem.role === 'assistant') {
                            // Update existing assistant response
                            lastItem.content = [{ type: 'text', text: partialResponse }];
                        }
                        else {
                            // Add new assistant response
                            newItems.push({
                                id: `assistant-${Date.now()}`,
                                type: 'message',
                                role: 'assistant',
                                content: [{ type: 'text', text: partialResponse }],
                                timestamp: Date.now()
                            });
                        }
                        return newItems;
                    });
                },
                onApprovalRequired: async (request) => {
                    // Handle approval request
                    return new Promise((resolve) => {
                        // For now, auto-approve based on policy
                        // In a full implementation, this would show an approval dialog
                        resolve(approvalPolicy !== 'suggest');
                    });
                },
                signal: abortControllerRef.current.signal
            });
            // Add final response to items
            if (response) {
                setItems(prev => {
                    const newItems = [...prev];
                    const lastItem = newItems[newItems.length - 1];
                    if (lastItem && lastItem.role === 'assistant') {
                        // Update existing response with final version
                        return [...newItems.slice(0, -1), response];
                    }
                    else {
                        // Add new response
                        return [...newItems, response];
                    }
                });
            }
        }
        catch (err) {
            if (err instanceof Error && err.name === 'AbortError') {
                // Request was cancelled
                setError('Request cancelled');
            }
            else {
                setError(err instanceof Error ? err.message : 'Unknown error occurred');
            }
        }
        finally {
            setLoading(false);
            abortControllerRef.current = null;
        }
    }, [loading, items, approvalPolicy]);
    const handleSlashCommand = (command) => {
        const [cmd, ...args] = command.slice(1).split(' ');
        switch (cmd.toLowerCase()) {
            case 'help':
                setOverlayMode("help");
                break;
            case 'model':
                setOverlayMode("model");
                break;
            case 'history':
                setOverlayMode("history");
                break;
            case 'approval':
                setOverlayMode("approval");
                break;
            case 'clear':
                setItems([]);
                setShowWelcome(true);
                break;
            case 'exit':
                handleExit();
                break;
            case 'status':
                showStatus();
                break;
            default:
                setError(`Unknown command: /${cmd}`);
        }
    };
    const showStatus = () => {
        const statusItem = {
            id: `status-${Date.now()}`,
            type: 'message',
            role: 'system',
            content: [{
                    type: 'text',
                    text: `Status:\n• Model: ${model}\n• Provider: ${provider}\n• Approval: ${approvalPolicy}\n• Messages: ${items.length}`
                }],
            timestamp: Date.now()
        };
        setItems(prev => [...prev, statusItem]);
    };
    const handleExit = () => {
        // Cancel any ongoing requests
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
        onExit();
    };
    const handleModelChange = (newModel, newProvider) => {
        setModel(newModel);
        setProvider(newProvider);
        setOverlayMode("none");
        // Show confirmation
        const confirmationItem = {
            id: `model-change-${Date.now()}`,
            type: 'message',
            role: 'system',
            content: [{
                    type: 'text',
                    text: `Switched to ${newModel} (${newProvider})`
                }],
            timestamp: Date.now()
        };
        setItems(prev => [...prev, confirmationItem]);
    };
    const handleApprovalModeChange = (newMode) => {
        setApprovalPolicy(newMode);
        setOverlayMode("none");
        // Show confirmation
        const confirmationItem = {
            id: `approval-change-${Date.now()}`,
            type: 'message',
            role: 'system',
            content: [{
                    type: 'text',
                    text: `Approval mode changed to: ${newMode}`
                }],
            timestamp: Date.now()
        };
        setItems(prev => [...prev, confirmationItem]);
    };
    // Render overlay if active
    if (overlayMode !== "none") {
        switch (overlayMode) {
            case "model":
                return ((0, jsx_runtime_1.jsx)(model_overlay_1.ModelOverlay, { currentModel: model, currentProvider: provider, onModelChange: handleModelChange, onClose: () => setOverlayMode("none") }));
            case "help":
                return ((0, jsx_runtime_1.jsx)(help_overlay_1.HelpOverlay, { onClose: () => setOverlayMode("none") }));
            case "history":
                return ((0, jsx_runtime_1.jsx)(history_overlay_1.HistoryOverlay, { items: items, onClose: () => setOverlayMode("none") }));
            case "approval":
                return ((0, jsx_runtime_1.jsx)(approval_mode_overlay_1.ApprovalModeOverlay, { currentMode: approvalPolicy, onModeChange: handleApprovalModeChange, onClose: () => setOverlayMode("none") }));
        }
    }
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", children: [(0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", flexGrow: 1, paddingX: 1, children: [showWelcome && (0, jsx_runtime_1.jsx)(app_1.WelcomeMessage, { config: config }), items.map((item) => ((0, jsx_runtime_1.jsx)(terminal_chat_response_item_1.TerminalChatResponseItem, { item: item }, item.id))), loading && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginY: 1, children: (0, jsx_runtime_1.jsx)(app_1.LoadingSpinner, { text: "AI is thinking..." }) })), error && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginY: 1, children: (0, jsx_runtime_1.jsx)(app_1.StatusIndicator, { status: "error", message: error }) }))] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { paddingX: 1, paddingY: 1, children: (0, jsx_runtime_1.jsx)(terminal_chat_input_1.TerminalChatInput, { onSendMessage: handleSendMessage, disabled: loading, placeholder: loading ? "AI is responding..." : "Type your message..." }) })] }));
}
//# sourceMappingURL=terminal-chat.js.map