import { ApprovalPolicy } from '../../types';
interface ApprovalModeOverlayProps {
    currentMode: ApprovalPolicy;
    onModeChange: (mode: ApprovalPolicy) => void;
    onClose: () => void;
}
export declare function ApprovalModeOverlay({ currentMode, onModeChange, onClose }: ApprovalModeOverlayProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=approval-mode-overlay.d.ts.map