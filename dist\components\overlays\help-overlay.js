"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HelpOverlay = HelpOverlay;
const jsx_runtime_1 = require("react/jsx-runtime");
const ink_1 = require("ink");
function HelpOverlay({ onClose }) {
    (0, ink_1.useInput)((input, key) => {
        if (key.escape || key.return || input === 'q') {
            onClose();
        }
    });
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", padding: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: "\uD83D\uDCDA Kritrima AI CLI Help" }) }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "yellow", bold: true, marginBottom: 1, children: "Slash Commands:" }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", paddingLeft: 2, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "/help" }), " - Show this help information"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "/model" }), " - Switch AI model or provider"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "/history" }), " - View conversation history"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "/clear" }), " - Clear current conversation"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "/approval" }), " - Change command approval mode"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "/status" }), " - Show current status and settings"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "/exit" }), " - Exit the application"] })] })] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "yellow", bold: true, marginBottom: 1, children: "Keyboard Shortcuts:" }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", paddingLeft: 2, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "Ctrl+C" }), " - Exit application"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "Ctrl+M" }), " - Open model selection"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "Ctrl+H" }), " - Show help (this screen)"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "Ctrl+R" }), " - View history"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "Esc" }), " - Close overlays"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "\u2191\u2193" }), " - Navigate command history"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "Tab" }), " - Autocomplete commands/files"] })] })] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "yellow", bold: true, marginBottom: 1, children: "File References:" }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", paddingLeft: 2, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { children: ["Use ", (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "@filename" }), " to reference files in your messages"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: ["Example: \"Analyze the code in ", (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "@src/app.tsx" }), "\""] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { children: "Tab completion works for file paths after @" })] })] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "yellow", bold: true, marginBottom: 1, children: "Approval Modes:" }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", paddingLeft: 2, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "red", children: "suggest" }), " - Manual approval for all commands"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "yellow", children: "auto-edit" }), " - Auto-approve safe commands, ask for others"] }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", children: "full-auto" }), " - Auto-approve all commands (use with caution)"] })] })] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "yellow", bold: true, marginBottom: 1, children: "Tips:" }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", paddingLeft: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { children: "\u2022 The AI can execute shell commands and modify files" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { children: "\u2022 Commands are sandboxed for security when possible" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { children: "\u2022 Use descriptive messages for better AI responses" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { children: "\u2022 Check approval mode if commands aren't executing" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { children: "\u2022 Git integration provides better context awareness" })] })] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Press Esc, Enter, or 'q' to close this help screen" }) })] }));
}
//# sourceMappingURL=help-overlay.js.map