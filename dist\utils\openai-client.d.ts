import { OpenAI } from 'openai';
export interface OpenAIClientOptions {
    provider?: string;
    apiKey?: string;
    baseURL?: string;
    timeout?: number;
    organization?: string;
    project?: string;
    maxRetries?: number;
}
/**
 * Create an OpenAI client instance for the specified provider
 */
export declare function createOpenAIClient(options?: OpenAIClientOptions): OpenAI;
/**
 * Test connection to the OpenAI API
 */
export declare function testConnection(provider?: string): Promise<boolean>;
/**
 * Get available models from the provider
 */
export declare function getAvailableModels(provider?: string): Promise<string[]>;
/**
 * Validate API key format for different providers
 */
export declare function validateApiKey(provider: string, apiKey: string): boolean;
/**
 * Get provider-specific headers
 */
export declare function getProviderHeaders(provider: string): Record<string, string>;
/**
 * Handle rate limiting with exponential backoff
 */
export declare function withRetry<T>(fn: () => Promise<T>, maxRetries?: number, baseDelay?: number): Promise<T>;
/**
 * Create a streaming completion with proper error handling
 */
export declare function createStreamingCompletion(client: OpenAI, params: OpenAI.ChatCompletionCreateParams): Promise<AsyncIterable<OpenAI.ChatCompletionChunk>>;
//# sourceMappingURL=openai-client.d.ts.map