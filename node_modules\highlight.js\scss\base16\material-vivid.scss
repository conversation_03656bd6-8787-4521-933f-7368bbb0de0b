pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Material Vivid
  Author: joshyrobot
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme material-vivid
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #202124  Default Background
base01  #27292c  Lighter Background (Used for status bars, line number and folding marks)
base02  #323639  Selection Background
base03  #44464d  Comments, Invisibles, Line Highlighting
base04  #676c71  Dark Foreground (Used for status bars)
base05  #80868b  Default Foreground, Caret, Delimiters, Operators
base06  #9e9e9e  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #f44336  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ff9800  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffeb3b  Classes, Markup Bold, Search Text Background
base0B  #00e676  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #00bcd4  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #2196f3  Functions, Methods, Attribute IDs, Headings
base0E  #673ab7  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #8d6e63  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #80868b;
  background: #202124
}
.hljs::selection,
.hljs ::selection {
  background-color: #323639;
  color: #80868b
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #44464d -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #44464d
}
/* base04 - #676c71 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #676c71
}
/* base05 - #80868b -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #80868b
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f44336
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ff9800
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffeb3b
}
.hljs-strong {
  font-weight: bold;
  color: #ffeb3b
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #00e676
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #00bcd4
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #2196f3
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #673ab7
}
.hljs-emphasis {
  color: #673ab7;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #8d6e63
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}