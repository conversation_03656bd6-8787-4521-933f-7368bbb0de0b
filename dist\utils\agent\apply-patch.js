"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatchConflictError = void 0;
exports.applyPatch = applyPatch;
exports.parsePatch = parsePatch;
exports.parseV4ADiff = parseV4ADiff;
exports.generateDiff = generateDiff;
exports.generateV4ADiff = generateV4ADiff;
exports.cleanupBackups = cleanupBackups;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const diff = __importStar(require("diff"));
const types_1 = require("../../types");
/**
 * Apply a unified diff patch to files
 */
async function applyPatch(patchContent, workdir = process.cwd(), options = {}) {
    const { dryRun = false, createBackup = true, allowConflicts = false, maxFileSize = 10 * 1024 * 1024 // 10MB
     } = options;
    const result = {
        success: false,
        filesModified: [],
        filesCreated: [],
        filesDeleted: [],
        conflicts: [],
        backupPaths: []
    };
    try {
        // Parse patch content
        const operations = parsePatch(patchContent);
        // Validate operations
        await validatePatchOperations(operations, workdir, maxFileSize);
        // Create backups if requested
        if (createBackup && !dryRun) {
            result.backupPaths = await createBackups(operations, workdir);
        }
        // Apply each operation
        for (const operation of operations) {
            try {
                await applyPatchOperation(operation, workdir, dryRun);
                switch (operation.type) {
                    case 'create':
                        result.filesCreated.push(operation.filePath);
                        break;
                    case 'modify':
                        result.filesModified.push(operation.filePath);
                        break;
                    case 'delete':
                        result.filesDeleted.push(operation.filePath);
                        break;
                    case 'rename':
                        result.filesModified.push(operation.filePath);
                        if (operation.newPath) {
                            result.filesCreated.push(operation.newPath);
                        }
                        break;
                }
            }
            catch (error) {
                if (error instanceof PatchConflictError) {
                    result.conflicts.push(...error.conflicts);
                    if (!allowConflicts) {
                        throw error;
                    }
                }
                else {
                    throw error;
                }
            }
        }
        result.success = result.conflicts.length === 0 || allowConflicts;
        return result;
    }
    catch (error) {
        result.error = error instanceof Error ? error.message : 'Unknown error';
        // Restore from backups if operation failed
        if (!dryRun && result.backupPaths.length > 0) {
            await restoreFromBackups(result.backupPaths, workdir);
        }
        return result;
    }
}
/**
 * Parse unified diff format into patch operations
 */
function parsePatch(patchContent) {
    const operations = [];
    const lines = patchContent.split('\n');
    let currentOperation = null;
    let currentHunk = null;
    let hunkLines = [];
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        // File header patterns
        if (line.startsWith('--- ')) {
            // Start of new file operation
            if (currentOperation) {
                if (currentHunk) {
                    currentHunk.lines = hunkLines;
                    currentOperation.hunks = currentOperation.hunks || [];
                    currentOperation.hunks.push(currentHunk);
                }
                operations.push(currentOperation);
            }
            currentOperation = {
                hunks: []
            };
            currentHunk = null;
            hunkLines = [];
            // Extract source file path
            const sourcePath = line.substring(4).trim();
            if (sourcePath !== '/dev/null') {
                currentOperation.filePath = sourcePath.replace(/^[ab]\//, '');
            }
        }
        else if (line.startsWith('+++ ')) {
            // Target file path
            if (currentOperation) {
                const targetPath = line.substring(4).trim();
                if (targetPath !== '/dev/null') {
                    if (!currentOperation.filePath) {
                        currentOperation.filePath = targetPath.replace(/^[ab]\//, '');
                        currentOperation.type = 'create';
                    }
                    else if (currentOperation.filePath !== targetPath.replace(/^[ab]\//, '')) {
                        currentOperation.newPath = targetPath.replace(/^[ab]\//, '');
                        currentOperation.type = 'rename';
                    }
                    else {
                        currentOperation.type = 'modify';
                    }
                }
                else {
                    currentOperation.type = 'delete';
                }
            }
        }
        else if (line.startsWith('@@')) {
            // Hunk header
            if (currentHunk) {
                currentHunk.lines = hunkLines;
                currentOperation.hunks.push(currentHunk);
            }
            const hunkMatch = line.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/);
            if (hunkMatch) {
                currentHunk = {
                    oldStart: parseInt(hunkMatch[1]),
                    oldLines: parseInt(hunkMatch[2] || '1'),
                    newStart: parseInt(hunkMatch[3]),
                    newLines: parseInt(hunkMatch[4] || '1'),
                    lines: []
                };
                hunkLines = [];
            }
        }
        else if (currentHunk && (line.startsWith(' ') || line.startsWith('+') || line.startsWith('-'))) {
            // Hunk content
            hunkLines.push(line);
        }
    }
    // Add final operation
    if (currentOperation) {
        if (currentHunk) {
            currentHunk.lines = hunkLines;
            currentOperation.hunks = currentOperation.hunks || [];
            currentOperation.hunks.push(currentHunk);
        }
        operations.push(currentOperation);
    }
    return operations;
}
/**
 * Parse V4A diff format (custom format for this project)
 */
function parseV4ADiff(diffContent) {
    const operations = [];
    const sections = diffContent.split(/\*\*\* \[(\w+)\] File: (.+)/);
    for (let i = 1; i < sections.length; i += 3) {
        const action = sections[i].toUpperCase();
        const filePath = sections[i + 1].trim();
        const content = sections[i + 2];
        switch (action) {
            case 'CREATE':
                operations.push({
                    type: 'create',
                    filePath,
                    content: content.trim()
                });
                break;
            case 'MODIFY':
                operations.push({
                    type: 'modify',
                    filePath,
                    content: content.trim()
                });
                break;
            case 'DELETE':
                operations.push({
                    type: 'delete',
                    filePath
                });
                break;
        }
    }
    return operations;
}
/**
 * Custom error class for patch conflicts
 */
class PatchConflictError extends Error {
    conflicts;
    constructor(conflicts) {
        super(`Patch conflicts detected in ${conflicts.length} locations`);
        this.conflicts = conflicts;
        this.name = 'PatchConflictError';
    }
}
exports.PatchConflictError = PatchConflictError;
/**
 * Validate patch operations before applying
 */
async function validatePatchOperations(operations, workdir, maxFileSize) {
    for (const operation of operations) {
        const fullPath = path.resolve(workdir, operation.filePath);
        // Security check: ensure path is within workdir
        if (!fullPath.startsWith(path.resolve(workdir))) {
            throw new types_1.SecurityError(`Path traversal detected: ${operation.filePath}`);
        }
        // Check file size limits
        if (operation.content && Buffer.byteLength(operation.content, 'utf8') > maxFileSize) {
            throw new types_1.ValidationError(`File too large: ${operation.filePath} (max: ${maxFileSize} bytes)`);
        }
        // Validate file operations
        switch (operation.type) {
            case 'create':
                if (fs.existsSync(fullPath)) {
                    throw new types_1.ValidationError(`File already exists: ${operation.filePath}`);
                }
                break;
            case 'modify':
            case 'delete':
                if (!fs.existsSync(fullPath)) {
                    throw new types_1.ValidationError(`File not found: ${operation.filePath}`);
                }
                break;
            case 'rename':
                if (!fs.existsSync(fullPath)) {
                    throw new types_1.ValidationError(`Source file not found: ${operation.filePath}`);
                }
                if (operation.newPath && fs.existsSync(path.resolve(workdir, operation.newPath))) {
                    throw new types_1.ValidationError(`Target file already exists: ${operation.newPath}`);
                }
                break;
        }
    }
}
/**
 * Apply a single patch operation
 */
async function applyPatchOperation(operation, workdir, dryRun) {
    const fullPath = path.resolve(workdir, operation.filePath);
    switch (operation.type) {
        case 'create':
            if (!dryRun) {
                await fs.ensureDir(path.dirname(fullPath));
                await fs.writeFile(fullPath, operation.content || '');
            }
            break;
        case 'delete':
            if (!dryRun) {
                await fs.remove(fullPath);
            }
            break;
        case 'modify':
            if (operation.hunks && operation.hunks.length > 0) {
                await applyHunks(fullPath, operation.hunks, dryRun);
            }
            else if (operation.content !== undefined) {
                if (!dryRun) {
                    await fs.writeFile(fullPath, operation.content);
                }
            }
            break;
        case 'rename':
            if (operation.newPath) {
                const newFullPath = path.resolve(workdir, operation.newPath);
                if (!dryRun) {
                    await fs.ensureDir(path.dirname(newFullPath));
                    await fs.move(fullPath, newFullPath);
                }
            }
            break;
    }
}
/**
 * Apply hunks to a file
 */
async function applyHunks(filePath, hunks, dryRun) {
    const originalContent = await fs.readFile(filePath, 'utf8');
    const originalLines = originalContent.split('\n');
    const newLines = [];
    const conflicts = [];
    let originalIndex = 0;
    for (const hunk of hunks) {
        // Copy lines before the hunk
        while (originalIndex < hunk.oldStart - 1) {
            newLines.push(originalLines[originalIndex]);
            originalIndex++;
        }
        // Apply hunk changes
        let hunkOriginalIndex = 0;
        let hunkNewIndex = 0;
        for (const line of hunk.lines) {
            const operation = line[0];
            const content = line.slice(1);
            switch (operation) {
                case ' ': // Context line
                    if (originalIndex < originalLines.length) {
                        if (originalLines[originalIndex] !== content) {
                            conflicts.push({
                                filePath,
                                line: originalIndex + 1,
                                expected: content,
                                actual: originalLines[originalIndex]
                            });
                        }
                        newLines.push(originalLines[originalIndex]);
                        originalIndex++;
                    }
                    hunkOriginalIndex++;
                    hunkNewIndex++;
                    break;
                case '-': // Deleted line
                    if (originalIndex < originalLines.length) {
                        if (originalLines[originalIndex] !== content) {
                            conflicts.push({
                                filePath,
                                line: originalIndex + 1,
                                expected: content,
                                actual: originalLines[originalIndex]
                            });
                        }
                        originalIndex++;
                    }
                    hunkOriginalIndex++;
                    break;
                case '+': // Added line
                    newLines.push(content);
                    hunkNewIndex++;
                    break;
            }
        }
    }
    // Copy remaining lines
    while (originalIndex < originalLines.length) {
        newLines.push(originalLines[originalIndex]);
        originalIndex++;
    }
    if (conflicts.length > 0) {
        throw new PatchConflictError(conflicts);
    }
    if (!dryRun) {
        await fs.writeFile(filePath, newLines.join('\n'));
    }
}
/**
 * Create backups of files that will be modified
 */
async function createBackups(operations, workdir) {
    const backupPaths = [];
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    for (const operation of operations) {
        if (operation.type === 'modify' || operation.type === 'delete' || operation.type === 'rename') {
            const fullPath = path.resolve(workdir, operation.filePath);
            if (fs.existsSync(fullPath)) {
                const backupPath = `${fullPath}.backup-${timestamp}`;
                await fs.copy(fullPath, backupPath);
                backupPaths.push(backupPath);
            }
        }
    }
    return backupPaths;
}
/**
 * Restore files from backups
 */
async function restoreFromBackups(backupPaths, workdir) {
    for (const backupPath of backupPaths) {
        const originalPath = backupPath.replace(/\.backup-[^.]+$/, '');
        try {
            if (fs.existsSync(backupPath)) {
                await fs.copy(backupPath, originalPath);
                await fs.remove(backupPath);
            }
        }
        catch (error) {
            console.warn(`Failed to restore backup ${backupPath}:`, error);
        }
    }
}
/**
 * Generate a unified diff between two strings
 */
function generateDiff(oldContent, newContent, filePath = 'file') {
    const patches = diff.createPatch(filePath, oldContent, newContent);
    return patches;
}
/**
 * Generate a V4A format diff
 */
function generateV4ADiff(operations) {
    const sections = [];
    for (const operation of operations) {
        switch (operation.type) {
            case 'create':
                sections.push(`*** [CREATE] File: ${operation.filePath}`);
                sections.push(operation.content || '');
                break;
            case 'modify':
                sections.push(`*** [MODIFY] File: ${operation.filePath}`);
                sections.push(operation.content || '');
                break;
            case 'delete':
                sections.push(`*** [DELETE] File: ${operation.filePath}`);
                break;
        }
    }
    return sections.join('\n\n');
}
/**
 * Clean up backup files older than specified days
 */
async function cleanupBackups(workdir, olderThanDays = 7) {
    const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
    let cleanedCount = 0;
    async function cleanupDirectory(dir) {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                await cleanupDirectory(fullPath);
            }
            else if (entry.name.includes('.backup-')) {
                const stats = await fs.stat(fullPath);
                if (stats.mtime.getTime() < cutoffTime) {
                    await fs.remove(fullPath);
                    cleanedCount++;
                }
            }
        }
    }
    await cleanupDirectory(workdir);
    return cleanedCount;
}
//# sourceMappingURL=apply-patch.js.map