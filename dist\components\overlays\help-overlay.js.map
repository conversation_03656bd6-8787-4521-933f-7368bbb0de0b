{"version": 3, "file": "help-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/help-overlay.tsx"], "names": [], "mappings": ";;AAOA,kCAqJC;;AA3JD,6BAA0C;AAM1C,SAAgB,WAAW,CAAC,EAAE,OAAO,EAAoB;IACvD,IAAA,cAAQ,EAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9C,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAE,CAAC,aAElD,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,wDAEhB,GACH,EAGN,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACzC,uBAAC,UAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,gCAElC,EAEP,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,CAAC,aACxC,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,sBAAa,qCAC3B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,uBAAc,sCAC5B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,yBAAgB,oCAC9B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,uBAAc,qCAC5B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,0BAAiB,uCAC/B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,wBAAe,2CAC7B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,sBAAa,+BAC3B,IACH,IACF,EAGN,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACzC,uBAAC,UAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,oCAElC,EAEP,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,CAAC,aACxC,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,uBAAc,2BAC3B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,uBAAc,+BAC3B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,uBAAc,kCAC3B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,uBAAc,uBAC3B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,oBAAW,yBACxB,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,6BAAU,mCACvB,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,oBAAW,sCACxB,IACH,IACF,EAGN,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACzC,uBAAC,UAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,iCAElC,EAEP,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,CAAC,aACxC,wBAAC,UAAI,uBACC,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,0BAAiB,4CACnC,EACP,wBAAC,UAAI,kDAC2B,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,6BAAoB,UAChE,EACP,uBAAC,UAAI,8DAEE,IACH,IACF,EAGN,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACzC,uBAAC,UAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,gCAElC,EAEP,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,CAAC,aACxC,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,KAAK,wBAAe,2CAC3B,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,QAAQ,0BAAiB,qDAChC,EACP,wBAAC,UAAI,eACH,uBAAC,UAAI,IAAC,KAAK,EAAC,OAAO,0BAAiB,uDAC/B,IACH,IACF,EAGN,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACzC,uBAAC,UAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,sBAElC,EAEP,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,CAAC,aACxC,uBAAC,UAAI,4EAEE,EACP,uBAAC,UAAI,2EAEE,EACP,uBAAC,UAAI,0EAEE,EACP,uBAAC,UAAI,0EAEE,EACP,uBAAC,UAAI,2EAEE,IACH,IACF,EAGN,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACf,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,mEAEX,GACH,IACF,CACP,CAAC;AACJ,CAAC"}