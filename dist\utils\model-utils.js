"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchModels = fetchModels;
exports.getModelInfoWithFallback = getModelInfoWithFallback;
exports.validateModelProvider = validateModelProvider;
exports.getRecommendedModel = getRecommendedModel;
exports.calculateContextUsage = calculateContextUsage;
exports.isContextNearLimit = isContextNearLimit;
exports.suggestContextOptimization = suggestContextOptimization;
exports.clearModelCache = clearModelCache;
exports.getCachedModels = getCachedModels;
exports.preloadModels = preloadModels;
const openai_client_1 = require("./openai-client");
const model_info_1 = require("./model-info");
const providers_1 = require("./providers");
// Cache for fetched models to avoid repeated API calls
const modelCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
/**
 * Fetch available models from a provider with caching
 */
async function fetchModels(provider) {
    const cacheKey = provider.toLowerCase();
    const cached = modelCache.get(cacheKey);
    // Return cached result if still valid
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.models;
    }
    try {
        // For local providers like Ollama, use predefined models
        if (provider.toLowerCase() === 'ollama') {
            const models = await fetchOllamaModels();
            modelCache.set(cacheKey, { models, timestamp: Date.now() });
            return models;
        }
        // For other providers, try to fetch from API
        const client = (0, openai_client_1.createOpenAIClient)({ provider });
        const response = await client.models.list();
        let models = response.data
            .map(model => model.id)
            .filter(id => isValidModel(id, provider))
            .sort();
        // Fallback to predefined models if API returns empty or invalid results
        if (models.length === 0) {
            models = (0, providers_1.getProviderModels)(provider);
        }
        modelCache.set(cacheKey, { models, timestamp: Date.now() });
        return models;
    }
    catch (error) {
        console.warn(`Failed to fetch models for ${provider}, using fallback:`, error);
        // Return predefined models as fallback
        const fallbackModels = (0, providers_1.getProviderModels)(provider);
        modelCache.set(cacheKey, { models: fallbackModels, timestamp: Date.now() });
        return fallbackModels;
    }
}
/**
 * Fetch models from Ollama
 */
async function fetchOllamaModels() {
    try {
        const response = await fetch('http://localhost:11434/api/tags');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        const data = await response.json();
        return data.models?.map((model) => model.name) || [];
    }
    catch (error) {
        // Return default Ollama models if API is not available
        return ['llama2', 'codellama', 'mistral', 'neural-chat'];
    }
}
/**
 * Check if a model ID is valid for the given provider
 */
function isValidModel(modelId, provider) {
    const providerId = provider.toLowerCase();
    switch (providerId) {
        case 'openai':
            return modelId.includes('gpt') || modelId.includes('o1') ||
                modelId.includes('text-') || modelId.includes('davinci');
        case 'gemini':
            return modelId.includes('gemini');
        case 'mistral':
            return modelId.includes('mistral') || modelId.includes('mixtral');
        case 'deepseek':
            return modelId.includes('deepseek');
        case 'xai':
            return modelId.includes('grok');
        case 'groq':
            return modelId.includes('mixtral') || modelId.includes('llama') ||
                modelId.includes('gemma');
        case 'openrouter':
            return true; // OpenRouter supports many models
        default:
            return true; // For unknown providers, accept all models
    }
}
/**
 * Get model information with fallback to API discovery
 */
async function getModelInfoWithFallback(modelId, provider) {
    // First try to get from predefined model info
    const info = (0, model_info_1.getModelInfo)(modelId);
    if (info) {
        return info;
    }
    // If not found, try to fetch from provider and create basic info
    try {
        const models = await fetchModels(provider);
        if (models.includes(modelId)) {
            return {
                id: modelId,
                name: modelId,
                provider,
                contextLength: getDefaultContextLength(provider),
                capabilities: getDefaultCapabilities(provider)
            };
        }
    }
    catch (error) {
        console.warn(`Failed to get model info for ${modelId}:`, error);
    }
    return null;
}
/**
 * Get default context length for a provider
 */
function getDefaultContextLength(provider) {
    switch (provider.toLowerCase()) {
        case 'openai':
            return 8192;
        case 'gemini':
            return 32768;
        case 'mistral':
            return 32768;
        case 'deepseek':
            return 32768;
        case 'xai':
            return 131072;
        case 'groq':
            return 32768;
        case 'ollama':
            return 4096;
        default:
            return 4096;
    }
}
/**
 * Get default capabilities for a provider
 */
function getDefaultCapabilities(provider) {
    switch (provider.toLowerCase()) {
        case 'openai':
            return ['text', 'function_calling', 'reasoning'];
        case 'gemini':
            return ['text', 'function_calling', 'vision'];
        case 'mistral':
            return ['text', 'function_calling'];
        case 'deepseek':
            return ['text', 'coding'];
        case 'xai':
            return ['text', 'reasoning'];
        case 'groq':
            return ['text', 'fast_inference'];
        case 'ollama':
            return ['text', 'local'];
        default:
            return ['text'];
    }
}
/**
 * Validate model compatibility with provider
 */
function validateModelProvider(modelId, provider) {
    const models = (0, providers_1.getProviderModels)(provider);
    if (models.length > 0) {
        return models.includes(modelId);
    }
    // If no predefined models, assume compatible
    return true;
}
/**
 * Get recommended model for a provider
 */
function getRecommendedModel(provider) {
    const models = (0, model_info_1.getModelsByProvider)(provider);
    if (models.length > 0) {
        // Return the first model as recommended
        return models[0].id;
    }
    // Fallback to predefined models
    const fallbackModels = (0, providers_1.getProviderModels)(provider);
    return fallbackModels[0] || 'gpt-4';
}
/**
 * Calculate context usage for a conversation
 */
function calculateContextUsage(messages, modelId) {
    const total = (0, model_info_1.getModelInfo)(modelId)?.contextLength || 4096;
    // Rough estimation: 1 token ≈ 4 characters
    const used = messages.reduce((sum, msg) => sum + Math.ceil(msg.content.length / 4), 0);
    return {
        used,
        total,
        percentage: Math.round((used / total) * 100)
    };
}
/**
 * Check if context is approaching limit
 */
function isContextNearLimit(messages, modelId, threshold = 0.8) {
    const usage = calculateContextUsage(messages, modelId);
    return usage.percentage >= threshold * 100;
}
/**
 * Suggest context optimization strategies
 */
function suggestContextOptimization(messages, modelId) {
    const usage = calculateContextUsage(messages, modelId);
    const suggestions = [];
    if (usage.percentage > 90) {
        suggestions.push('Context is nearly full. Consider starting a new conversation.');
    }
    else if (usage.percentage > 70) {
        suggestions.push('Context is getting large. Consider summarizing older messages.');
    }
    if (messages.length > 20) {
        suggestions.push('Many messages in conversation. Consider compacting history.');
    }
    return suggestions;
}
/**
 * Clear model cache
 */
function clearModelCache() {
    modelCache.clear();
}
/**
 * Get cached models without API call
 */
function getCachedModels(provider) {
    const cached = modelCache.get(provider.toLowerCase());
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.models;
    }
    return null;
}
/**
 * Preload models for common providers
 */
async function preloadModels(providers = ['openai', 'gemini', 'mistral']) {
    const promises = providers.map(provider => fetchModels(provider).catch(error => console.warn(`Failed to preload models for ${provider}:`, error)));
    await Promise.allSettled(promises);
}
//# sourceMappingURL=model-utils.js.map