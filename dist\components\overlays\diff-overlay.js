"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiffOverlay = DiffOverlay;
exports.calculateDiffStats = calculateDiffStats;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const ink_1 = require("ink");
function DiffOverlay({ filePath, oldContent, newContent, onApprove, onReject, onClose }) {
    const [diffLines, setDiffLines] = (0, react_1.useState)([]);
    const [scrollPosition, setScrollPosition] = (0, react_1.useState)(0);
    const [selectedAction, setSelectedAction] = (0, react_1.useState)('approve');
    const [viewHeight] = (0, react_1.useState)(20); // Visible lines in diff view
    (0, react_1.useEffect)(() => {
        const diff = generateDiff(oldContent, newContent);
        setDiffLines(diff);
    }, [oldContent, newContent]);
    (0, ink_1.useInput)((input, key) => {
        if (key.escape) {
            onClose();
            return;
        }
        if (key.return) {
            if (selectedAction === 'approve') {
                onApprove();
            }
            else {
                onReject();
            }
            return;
        }
        if (key.tab) {
            setSelectedAction(prev => prev === 'approve' ? 'reject' : 'approve');
            return;
        }
        if (key.upArrow) {
            setScrollPosition(prev => Math.max(0, prev - 1));
            return;
        }
        if (key.downArrow) {
            setScrollPosition(prev => Math.min(diffLines.length - viewHeight, prev + 1));
            return;
        }
        if (key.pageUp) {
            setScrollPosition(prev => Math.max(0, prev - viewHeight));
            return;
        }
        if (key.pageDown) {
            setScrollPosition(prev => Math.min(diffLines.length - viewHeight, prev + viewHeight));
            return;
        }
        if (input === 'a' || input === 'A') {
            onApprove();
            return;
        }
        if (input === 'r' || input === 'R') {
            onReject();
            return;
        }
    });
    const visibleLines = diffLines.slice(scrollPosition, scrollPosition + viewHeight);
    const hasMoreAbove = scrollPosition > 0;
    const hasMoreBelow = scrollPosition + viewHeight < diffLines.length;
    const getLineColor = (line) => {
        switch (line.type) {
            case 'added':
                return 'green';
            case 'removed':
                return 'red';
            case 'context':
            default:
                return 'gray';
        }
    };
    const getLinePrefix = (line) => {
        switch (line.type) {
            case 'added':
                return '+';
            case 'removed':
                return '-';
            case 'context':
            default:
                return ' ';
        }
    };
    const formatLineNumbers = (line) => {
        const oldNum = line.oldLineNumber ? line.oldLineNumber.toString().padStart(4) : '    ';
        const newNum = line.newLineNumber ? line.newLineNumber.toString().padStart(4) : '    ';
        return `${oldNum} ${newNum}`;
    };
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", padding: 1, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "cyan", bold: true, children: ["\uD83D\uDCDD File Diff: ", filePath] }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", children: ["Lines: ", diffLines.filter(l => l.type === 'added').length, " added, ", ' ', diffLines.filter(l => l.type === 'removed').length, " removed, ", ' ', diffLines.filter(l => l.type === 'context').length, " unchanged"] }) }), hasMoreAbove && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "\u25B2 More lines above (scroll up to see)" }) })), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", flex: 1, borderStyle: "round", paddingX: 1, children: [visibleLines.map((line, index) => ((0, jsx_runtime_1.jsxs)(ink_1.Box, { children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: formatLineNumbers(line) }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: getLineColor(line), children: [getLinePrefix(line), " ", line.content] })] }, scrollPosition + index))), diffLines.length === 0 && ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "No differences found" }))] }), hasMoreBelow && ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "\u25BC More lines below (scroll down to see)" }) })), (0, jsx_runtime_1.jsxs)(ink_1.Box, { marginTop: 2, justifyContent: "center", children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginRight: 4, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: selectedAction === 'approve' ? 'green' : 'gray', bold: true, children: [selectedAction === 'approve' ? '▶ ' : '  ', "[A]pprove"] }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: selectedAction === 'reject' ? 'red' : 'gray', bold: true, children: [selectedAction === 'reject' ? '▶ ' : '  ', "[R]eject"] }) })] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { marginTop: 1, flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Use \u2191\u2193 to scroll, PgUp/PgDn for page scroll, Tab to switch action" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: "Press A to approve, R to reject, Enter to confirm, Esc to close" })] })] }));
}
/**
 * Generate diff lines from old and new content
 */
function generateDiff(oldContent, newContent) {
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    const diffLines = [];
    // Simple line-by-line diff algorithm
    // In a production implementation, you'd use a more sophisticated algorithm like Myers
    let oldIndex = 0;
    let newIndex = 0;
    while (oldIndex < oldLines.length || newIndex < newLines.length) {
        const oldLine = oldLines[oldIndex];
        const newLine = newLines[newIndex];
        if (oldIndex >= oldLines.length) {
            // Only new lines remaining
            diffLines.push({
                type: 'added',
                content: newLine,
                newLineNumber: newIndex + 1
            });
            newIndex++;
        }
        else if (newIndex >= newLines.length) {
            // Only old lines remaining
            diffLines.push({
                type: 'removed',
                content: oldLine,
                oldLineNumber: oldIndex + 1
            });
            oldIndex++;
        }
        else if (oldLine === newLine) {
            // Lines are the same
            diffLines.push({
                type: 'context',
                content: oldLine,
                oldLineNumber: oldIndex + 1,
                newLineNumber: newIndex + 1
            });
            oldIndex++;
            newIndex++;
        }
        else {
            // Lines are different - look ahead to see if we can find a match
            let foundMatch = false;
            const lookAhead = 5;
            // Look for the new line in upcoming old lines
            for (let i = 1; i <= lookAhead && oldIndex + i < oldLines.length; i++) {
                if (oldLines[oldIndex + i] === newLine) {
                    // Found match - mark intermediate old lines as removed
                    for (let j = 0; j < i; j++) {
                        diffLines.push({
                            type: 'removed',
                            content: oldLines[oldIndex + j],
                            oldLineNumber: oldIndex + j + 1
                        });
                    }
                    oldIndex += i;
                    foundMatch = true;
                    break;
                }
            }
            if (!foundMatch) {
                // Look for the old line in upcoming new lines
                for (let i = 1; i <= lookAhead && newIndex + i < newLines.length; i++) {
                    if (newLines[newIndex + i] === oldLine) {
                        // Found match - mark intermediate new lines as added
                        for (let j = 0; j < i; j++) {
                            diffLines.push({
                                type: 'added',
                                content: newLines[newIndex + j],
                                newLineNumber: newIndex + j + 1
                            });
                        }
                        newIndex += i;
                        foundMatch = true;
                        break;
                    }
                }
            }
            if (!foundMatch) {
                // No match found - treat as removed and added
                diffLines.push({
                    type: 'removed',
                    content: oldLine,
                    oldLineNumber: oldIndex + 1
                });
                diffLines.push({
                    type: 'added',
                    content: newLine,
                    newLineNumber: newIndex + 1
                });
                oldIndex++;
                newIndex++;
            }
        }
    }
    return diffLines;
}
/**
 * Calculate diff statistics
 */
function calculateDiffStats(oldContent, newContent) {
    const diff = generateDiff(oldContent, newContent);
    return {
        linesAdded: diff.filter(line => line.type === 'added').length,
        linesRemoved: diff.filter(line => line.type === 'removed').length,
        linesChanged: diff.filter(line => line.type === 'context').length
    };
}
//# sourceMappingURL=diff-overlay.js.map