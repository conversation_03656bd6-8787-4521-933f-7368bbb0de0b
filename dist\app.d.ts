import React from 'react';
import { AppConfig } from './types';
interface AppProps {
    config: AppConfig;
    initialMessage?: string;
    verbose?: boolean;
}
export declare function App({ config, initialMessage, verbose }: AppProps): import("react/jsx-runtime").JSX.Element;
/**
 * Error boundary component for the app
 */
interface ErrorBoundaryProps {
    children: React.ReactNode;
    fallback?: React.ComponentType<{
        error: Error;
    }>;
}
interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
}
export declare class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps);
    static getDerivedStateFromError(error: Error): ErrorBoundaryState;
    componentDidCatch(error: Error, errorInfo: React.ErrorInfo): void;
    render(): string | number | boolean | import("react/jsx-runtime").JSX.Element | Iterable<React.ReactNode> | null | undefined;
}
/**
 * App wrapper with error boundary
 */
export declare function AppWithErrorBoundary(props: AppProps): import("react/jsx-runtime").JSX.Element;
/**
 * Welcome message component
 */
export declare function WelcomeMessage({ config }: {
    config: AppConfig;
}): import("react/jsx-runtime").JSX.Element;
/**
 * Loading component
 */
export declare function LoadingSpinner({ text }: {
    text?: string;
}): import("react/jsx-runtime").JSX.Element;
/**
 * Status indicator component
 */
export declare function StatusIndicator({ status, message }: {
    status: 'success' | 'error' | 'warning' | 'info';
    message: string;
}): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=app.d.ts.map