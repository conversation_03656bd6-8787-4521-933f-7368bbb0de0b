{"version": 3, "file": "raw-exec.js", "sourceRoot": "", "sources": ["../../../../src/utils/agent/sandbox/raw-exec.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,oBA+JC;AAKD,sDAWC;AAoFD,4DAYC;AAKD,kCAEC;AAKD,4CAEC;AAtSD,iDAAsC;AACtC,2CAA6B;AAC7B,0CAAiF;AACjF,4DAAmF;AAEnF;;;GAGG;AACI,KAAK,UAAU,IAAI,CACxB,KAAgB,EAChB,MAAiB,EACjB,WAAyB;IAEzB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC7B,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,KAAK,EAAE,GAAG,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;IAElH,iBAAiB;IACjB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACrC,MAAM,IAAI,qBAAa,CAAC,2BAA2B,CAAC,CAAC;IACvD,CAAC;IAED,qCAAqC;IACrC,MAAM,cAAc,GAAG,IAAA,gCAAY,EAAC,OAAO,CAAC,CAAC;IAE7C,4BAA4B;IAC5B,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAE9C,sDAAsD;IACtD,IAAI,CAAC;QACH,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,qBAAa,CAAC,qCAAqC,eAAe,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YACzB,MAAM,IAAI,qBAAa,CAAC,yCAAyC,eAAe,EAAE,CAAC,CAAC;QACtF,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,qBAAa;YAAE,MAAM,KAAK,CAAC;QAChD,MAAM,IAAI,qBAAa,CAAC,oCAAoC,eAAe,EAAE,CAAC,CAAC;IACjF,CAAC;IAED,sBAAsB;IACtB,MAAM,OAAO,GAAG;QACd,GAAG,OAAO,CAAC,GAAG;QACd,GAAG,GAAG;QACN,2BAA2B;QAC3B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE;QAC5B,uCAAuC;QACvC,GAAG,EAAE,eAAe;KACrB,CAAC;IAEF,kCAAkC;IAClC,MAAM,KAAK,GAAG,IAAA,mCAAe,GAAE,CAAC;IAChC,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC/C,MAAM,SAAS,GAAG,IAAA,gCAAY,EAAC,aAAa,CAAC,CAAC;IAE9C,OAAO,IAAI,OAAO,CAAa,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjD,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,uBAAuB;QACvB,MAAM,KAAK,GAAG,IAAA,qBAAK,EAAC,KAAK,EAAE,SAAS,EAAE;YACpC,GAAG,EAAE,eAAe;YACpB,GAAG,EAAE,OAAO;YACZ,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;YAC/B,KAAK,EAAE,KAAK,EAAE,4BAA4B;YAC1C,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;QAEH,sBAAsB;QACtB,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtB,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC,EAAE,IAAI,CAAC,CAAC;YACX,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACtD,CAAC;QAED,cAAc;QACd,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACtB,UAAU,CAAC,GAAG,EAAE;oBACd,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;wBAClB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC,EAAE,IAAI,CAAC,CAAC;YACX,CAAC;QACH,CAAC,EAAE,OAAO,CAAC,CAAC;QAEZ,iBAAiB;QACjB,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YACxC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YACxC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,KAAK,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,EAAE;YACvC,IAAI,UAAU;gBAAE,OAAO;YACvB,UAAU,GAAG,IAAI,CAAC;YAElB,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,CAAC;YAE3B,MAAM,MAAM,GAAe;gBACzB,OAAO,EAAE,QAAQ,KAAK,CAAC;gBACvB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACrB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACrB,QAAQ;gBACR,QAAQ;gBACR,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,eAAe;aACzB,CAAC;YAEF,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YAC1B,IAAI,UAAU;gBAAE,OAAO;YACvB,UAAU,GAAG,IAAI,CAAC;YAElB,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACzD,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAExC,MAAM,MAAM,GAAe;gBACzB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gBACrB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,OAAO;gBACtC,QAAQ,EAAE,CAAC;gBACX,QAAQ;gBACR,OAAO,EAAE,cAAc;gBACvB,OAAO,EAAE,eAAe;gBACxB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC;YAEF,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACrB,+BAA+B;QACjC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,qBAAqB,CACzC,KAAgB,EAChB,MAAiB,EACjB,0BAAiD,EAAE,EACnD,WAAyB;IAEzB,oCAAoC;IACpC,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,uBAAuB,CAAC,CAAC;IAEhE,wBAAwB;IACxB,OAAO,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAC9B,KAAgB,EAChB,MAAiB,EACjB,uBAA8C;IAE9C,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,GAAG,KAAK,CAAC;IAErE,uCAAuC;IACvC,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;IAEtD,mCAAmC;IACnC,MAAM,iBAAiB,GAAG;QACxB,UAAU;QACV,gBAAgB;QAChB,WAAW;QACX,iBAAiB;QACjB,SAAS;QACT,eAAe,EAAG,YAAY;QAC9B,WAAW;QACX,YAAY;KACb,CAAC;IAEF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;QACxC,IAAI,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,qBAAa,CAAC,uCAAuC,OAAO,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED,qDAAqD;IACrD,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC9C,MAAM,YAAY,GAAG;QACnB,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE;QAC/B,GAAG,uBAAuB;KAC3B,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAElC,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACzC,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,CACjC,CAAC;IAEF,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,MAAM,IAAI,qBAAa,CAAC,2CAA2C,eAAe,EAAE,CAAC,CAAC;IACxF,CAAC;IAED,gDAAgD;IAChD,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;QAC1B,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAChD,wBAAwB;YACxB,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,eAAe,EAAE,YAAY,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,qBAAa,CAAC,wCAAwC,GAAG,EAAE,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAC1B,GAAW,EACX,OAAe,EACf,YAAsB;IAEtB,IAAI,CAAC;QACH,uCAAuC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAEhD,oDAAoD;QACpD,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC9B,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAC9B,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,+CAA+C;QAC/C,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB;IAMtC,OAAO;QACL,SAAS,EAAE,KAAK;QAChB,iBAAiB,EAAE,KAAK;QACxB,oBAAoB,EAAE,KAAK;QAC3B,iBAAiB,EAAE,KAAK;KACzB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW;IACzB,OAAO,IAAI,CAAC,CAAC,gDAAgD;AAC/D,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB;IAC9B,OAAO,4CAA4C,CAAC;AACtD,CAAC"}