"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApprovalModeOverlay = ApprovalModeOverlay;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const ink_1 = require("ink");
const APPROVAL_MODES = [
    {
        mode: 'suggest',
        name: 'Suggest Mode',
        description: 'Manual approval required for all commands',
        color: 'red',
        icon: '🔒',
        details: [
            '• All commands require explicit approval',
            '• Maximum security and control',
            '• Best for sensitive environments',
            '• Recommended for production systems'
        ]
    },
    {
        mode: 'auto-edit',
        name: 'Auto-Edit Mode',
        description: 'Auto-approve safe commands, ask for dangerous ones',
        color: 'yellow',
        icon: '⚡',
        details: [
            '• Safe read-only commands auto-approved',
            '• File modifications and system commands require approval',
            '• Balanced security and convenience',
            '• Good for development environments'
        ]
    },
    {
        mode: 'full-auto',
        name: 'Full-Auto Mode',
        description: 'Auto-approve all commands (use with caution)',
        color: 'green',
        icon: '🚀',
        details: [
            '• All commands executed automatically',
            '• Maximum speed and convenience',
            '• Commands still run in sandbox when available',
            '• Use only in trusted, isolated environments'
        ]
    }
];
function ApprovalModeOverlay({ currentMode, onModeChange, onClose }) {
    const [selectedIndex, setSelectedIndex] = (0, react_1.useState)(APPROVAL_MODES.findIndex(mode => mode.mode === currentMode));
    (0, ink_1.useInput)((input, key) => {
        if (key.escape) {
            onClose();
            return;
        }
        if (key.return) {
            const selectedMode = APPROVAL_MODES[selectedIndex];
            if (selectedMode) {
                onModeChange(selectedMode.mode);
            }
            return;
        }
        if (key.upArrow) {
            setSelectedIndex(prev => prev > 0 ? prev - 1 : APPROVAL_MODES.length - 1);
            return;
        }
        if (key.downArrow) {
            setSelectedIndex(prev => prev < APPROVAL_MODES.length - 1 ? prev + 1 : 0);
            return;
        }
        // Quick selection by number
        const num = parseInt(input);
        if (num >= 1 && num <= APPROVAL_MODES.length) {
            setSelectedIndex(num - 1);
            return;
        }
    });
    const selectedMode = APPROVAL_MODES[selectedIndex];
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", padding: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: "\uD83D\uDD10 Command Approval Mode" }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, paddingX: 2, borderStyle: "round", children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: ["Current mode: ", (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: currentMode })] }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", flex: 1, children: APPROVAL_MODES.map((mode, index) => ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: index === selectedIndex ? 'cyan' : mode.color, bold: true, children: [index === selectedIndex ? '▶ ' : '  ', mode.icon, " ", mode.name, mode.mode === currentMode && ' (current)'] }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { paddingLeft: 4, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: mode.description }) }), index === selectedIndex && ((0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", paddingLeft: 4, marginTop: 1, children: mode.details.map((detail, detailIndex) => ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: detail }, detailIndex))) }))] }, mode.mode))) }), selectedMode && selectedMode.mode === 'full-auto' && ((0, jsx_runtime_1.jsxs)(ink_1.Box, { marginY: 2, paddingX: 2, borderStyle: "round", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "red", bold: true, children: "\u26A0\uFE0F  WARNING: Full-Auto Mode" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "red", children: "This mode will execute all AI-suggested commands automatically. Only use in trusted, isolated environments." })] })), (0, jsx_runtime_1.jsxs)(ink_1.Box, { marginTop: 2, flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Use \u2191\u2193 to navigate, Enter to select, Esc to cancel" }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", dimColor: true, children: ["You can also press 1-", APPROVAL_MODES.length, " for quick selection"] })] })] }));
}
//# sourceMappingURL=approval-mode-overlay.js.map