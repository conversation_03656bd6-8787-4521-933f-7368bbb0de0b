/**
 * Get current platform
 */
export declare function getCurrentPlatform(): 'windows' | 'unix';
/**
 * Adapt command for current platform
 */
export declare function adaptCommand(command: string[]): string[];
/**
 * Get shell for current platform
 */
export declare function getDefaultShell(): string;
/**
 * Get shell arguments for command execution
 */
export declare function getShellArgs(command: string): string[];
//# sourceMappingURL=platform-commands.d.ts.map