"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorBoundary = void 0;
exports.App = App;
exports.AppWithErrorBoundary = AppWithErrorBoundary;
exports.WelcomeMessage = WelcomeMessage;
exports.LoadingSpinner = LoadingSpinner;
exports.StatusIndicator = StatusIndicator;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = __importStar(require("react"));
const ink_1 = require("ink");
const terminal_chat_1 = require("./components/chat/terminal-chat");
const config_1 = require("./utils/config");
const sandbox_1 = require("./utils/agent/sandbox");
function App({ config, initialMessage, verbose }) {
    const { exit } = (0, ink_1.useApp)();
    const [isReady, setIsReady] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const [warnings, setWarnings] = (0, react_1.useState)([]);
    (0, react_1.useEffect)(() => {
        async function initialize() {
            try {
                const newWarnings = [];
                // Check if we're in a git repository
                if (!(0, config_1.isInGitRepository)()) {
                    newWarnings.push('Not in a git repository - some features may be limited');
                }
                // Check sandbox capabilities
                const sandbox = (0, sandbox_1.getSandboxCapabilities)();
                if (!sandbox.sandboxed && verbose) {
                    newWarnings.push(`Using ${sandbox.name} - limited security isolation`);
                }
                // Validate working directory
                if (config.workdir) {
                    const fs = require('fs');
                    if (!fs.existsSync(config.workdir)) {
                        throw new Error(`Working directory does not exist: ${config.workdir}`);
                    }
                }
                setWarnings(newWarnings);
                setIsReady(true);
            }
            catch (err) {
                setError(err instanceof Error ? err.message : 'Initialization failed');
            }
        }
        initialize();
    }, [config, verbose]);
    // Handle app exit
    const handleExit = () => {
        exit();
    };
    if (error) {
        return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", padding: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "red", children: ["\u274C Error: ", error] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Press Ctrl+C to exit" })] }));
    }
    if (!isReady) {
        return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", padding: 1, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "\uD83D\uDE80 Initializing Kritrima AI..." }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Loading configuration and checking environment..." })] }));
    }
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", children: [(0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginBottom: 1, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: "\uD83E\uDD16 Kritrima AI CLI" }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", children: ["Model: ", config.model, " | Provider: ", config.provider, " | Mode: ", config.approvalMode] }), warnings.length > 0 && ((0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", marginTop: 1, children: warnings.map((warning, index) => ((0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "yellow", children: ["\u26A0\uFE0F  ", warning] }, index))) })), (0, config_1.isInGitRepository)() && verbose && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "green", children: ["\uD83D\uDCC1 Git repository: ", (0, config_1.getGitRepositoryRoot)()] })), verbose && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "blue", children: ["\uD83D\uDD12 Security: ", (0, sandbox_1.getSandboxCapabilities)().name] }))] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { flex: 1, children: (0, jsx_runtime_1.jsx)(terminal_chat_1.TerminalChat, { config: config, initialMessage: initialMessage, onExit: handleExit }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 1, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", dimColor: true, children: "Press Ctrl+C to exit | Type /help for commands | Type /model to switch models" }) })] }));
}
class ErrorBoundary extends react_1.default.Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }
    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }
    componentDidCatch(error, errorInfo) {
        console.error('React Error Boundary caught an error:', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            const FallbackComponent = this.props.fallback || DefaultErrorFallback;
            return (0, jsx_runtime_1.jsx)(FallbackComponent, { error: this.state.error });
        }
        return this.props.children;
    }
}
exports.ErrorBoundary = ErrorBoundary;
/**
 * Default error fallback component
 */
function DefaultErrorFallback({ error }) {
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", padding: 1, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "red", bold: true, children: "\uD83D\uDCA5 Application Error" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "red", children: error.message }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", marginTop: 1, children: "Please check your configuration and try again." }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Press Ctrl+C to exit" })] }));
}
/**
 * App wrapper with error boundary
 */
function AppWithErrorBoundary(props) {
    return ((0, jsx_runtime_1.jsx)(ErrorBoundary, { children: (0, jsx_runtime_1.jsx)(App, { ...props }) }));
}
/**
 * Welcome message component
 */
function WelcomeMessage({ config }) {
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", padding: 1, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: "Welcome to Kritrima AI! \uD83D\uDE80" }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { children: ["You're connected to ", config.provider, " using ", config.model] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", marginTop: 1, children: "Available commands:" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "\u2022 /help - Show help information" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "\u2022 /model - Switch AI model or provider" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "\u2022 /history - View command history" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "\u2022 /clear - Clear conversation" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "\u2022 /exit - Exit the application" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "green", marginTop: 1, children: "Start typing your message below..." })] }));
}
/**
 * Loading component
 */
function LoadingSpinner({ text = "Loading..." }) {
    const [frame, setFrame] = (0, react_1.useState)(0);
    const frames = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
    (0, react_1.useEffect)(() => {
        const interval = setInterval(() => {
            setFrame(prev => (prev + 1) % frames.length);
        }, 100);
        return () => clearInterval(interval);
    }, []);
    return ((0, jsx_runtime_1.jsx)(ink_1.Box, { children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "blue", children: [frames[frame], " ", text] }) }));
}
/**
 * Status indicator component
 */
function StatusIndicator({ status, message }) {
    const getStatusIcon = () => {
        switch (status) {
            case 'success':
                return '✅';
            case 'error':
                return '❌';
            case 'warning':
                return '⚠️';
            case 'info':
            default:
                return 'ℹ️';
        }
    };
    const getStatusColor = () => {
        switch (status) {
            case 'success':
                return 'green';
            case 'error':
                return 'red';
            case 'warning':
                return 'yellow';
            case 'info':
            default:
                return 'blue';
        }
    };
    return ((0, jsx_runtime_1.jsx)(ink_1.Box, { children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: getStatusColor(), children: [getStatusIcon(), " ", message] }) }));
}
//# sourceMappingURL=app.js.map