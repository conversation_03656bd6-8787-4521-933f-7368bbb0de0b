"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// Test setup file for Jest
require("jest");
// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.OPENAI_API_KEY = 'test-key';
// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
beforeEach(() => {
    // Reset console mocks before each test
    console.error = jest.fn();
    console.warn = jest.fn();
});
afterEach(() => {
    // Restore console methods after each test
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
});
// Global test utilities
global.testUtils = {
    createMockConfig: () => ({
        model: 'gpt-4',
        provider: 'openai',
        approvalMode: 'suggest',
        maxTokens: 4096,
        temperature: 0.7,
        timeout: 30000,
        workdir: process.cwd(),
        additionalWritableRoots: []
    }),
    createMockResponseItem: (overrides = {}) => ({
        id: 'test-id',
        type: 'message',
        role: 'assistant',
        content: [{ type: 'text', text: 'Test response' }],
        timestamp: Date.now(),
        ...overrides
    })
};
//# sourceMappingURL=test-setup.js.map