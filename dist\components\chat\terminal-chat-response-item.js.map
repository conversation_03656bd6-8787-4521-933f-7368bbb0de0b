{"version": 3, "file": "terminal-chat-response-item.js", "sourceRoot": "", "sources": ["../../../src/components/chat/terminal-chat-response-item.tsx"], "names": [], "mappings": ";;AAQA,4DAiIC;;AAxID,6BAAgC;AAOhC,SAAgB,wBAAwB,CAAC,EAAE,IAAI,EAAiC;IAC9E,MAAM,eAAe,GAAG,CAAC,SAAiB,EAAE,EAAE;QAC5C,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC;IAClD,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACzC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrB,KAAK,MAAM;oBACT,OAAO,CACL,uBAAC,SAAG,IAAa,aAAa,EAAC,QAAQ,YACrC,uBAAC,UAAI,cAAE,OAAO,CAAC,IAAI,GAAQ,IADnB,KAAK,CAET,CACP,CAAC;gBAEJ,KAAK,eAAe;oBAClB,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;wBAC1B,OAAO,CACL,wBAAC,SAAG,IAAa,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,OAAO,aAClF,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,mDACF,OAAO,CAAC,aAAa,CAAC,IAAI,IACxC,EACP,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,4BACJ,OAAO,CAAC,aAAa,CAAC,SAAS,IACtC,KANC,KAAK,CAOT,CACP,CAAC;oBACJ,CAAC;oBACD,MAAM;gBAER,KAAK,iBAAiB;oBACpB,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;wBAC5B,OAAO,CACL,wBAAC,SAAG,IAAa,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,OAAO,aAClF,wBAAC,UAAI,IAAC,KAAK,EAAE,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,mBACjE,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,wBACvC,EACP,uBAAC,UAAI,cAAE,OAAO,CAAC,eAAe,CAAC,MAAM,GAAQ,EAC5C,OAAO,CAAC,eAAe,CAAC,QAAQ,IAAI,CACnC,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,iCACd,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,IAC/D,CACR,KATO,KAAK,CAUT,CACP,CAAC;oBACJ,CAAC;oBACD,MAAM;gBAER;oBACE,OAAO,IAAI,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC;YACd,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC;YACd,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC;YACd,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB,KAAK,WAAW;gBACd,OAAO,OAAO,CAAC;YACjB,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAEpC,wBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,aAClB,wBAAC,UAAI,IAAC,KAAK,EAAE,YAAY,EAAE,EAAE,IAAI,mBAC9B,WAAW,EAAE,OAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAClE,EACP,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,QAAC,UAAU,EAAE,CAAC,YACtC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,GAC3B,EACN,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CACxB,uBAAC,UAAI,IAAC,KAAK,EAAC,KAAK,EAAC,UAAU,EAAE,CAAC,wBAExB,CACR,IACG,EAGN,uBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,WAAW,EAAE,CAAC,YACvC,aAAa,EAAE,GACZ,EAGL,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CACzD,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,YAC/B,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,mBACxB,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CACxB,wBAAC,UAAI,4BAAW,IAAI,CAAC,QAAQ,CAAC,OAAO,SAAS,CAC/C,EACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,SAAS,IAAI,CACvC,wBAAC,UAAI,yBAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS,CAC7C,EACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CACzB,wBAAC,UAAI,6BAAY,IAAI,CAAC,QAAQ,CAAC,QAAQ,WAAW,CACnD,EACA,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,SAAS,IAAI,CACtC,uBAAC,UAAI,IAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,YACjD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAC7B,CACR,IACI,GACH,CACP,IACG,CACP,CAAC;AACJ,CAAC"}