"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.chunkToResponseEvent = chunkToResponseEvent;
exports.streamResponses = streamResponses;
exports.inputItemsToMessages = inputItemsToMessages;
exports.createResponseItemFromEvents = createResponseItemFromEvents;
exports.createFunctionResultItem = createFunctionResultItem;
exports.createErrorItem = createErrorItem;
exports.estimateTokenCount = estimateTokenCount;
exports.calculateConversationTokens = calculateConversationTokens;
exports.truncateConversation = truncateConversation;
/**
 * Convert OpenAI chat completion chunk to response event
 */
function chunkToResponseEvent(chunk) {
    const choice = chunk.choices?.[0];
    if (!choice)
        return null;
    const { delta, finish_reason } = choice;
    // Handle content delta
    if (delta.content) {
        return {
            type: 'response.output_text.delta',
            delta: delta.content
        };
    }
    // Handle function call start
    if (delta.function_call) {
        if (delta.function_call.name) {
            return {
                type: 'response.function_call.start',
                function_call: {
                    name: delta.function_call.name,
                    arguments: delta.function_call.arguments || '',
                    id: chunk.id || ''
                }
            };
        }
        if (delta.function_call.arguments) {
            return {
                type: 'response.function_call.arguments.delta',
                delta: delta.function_call.arguments
            };
        }
    }
    // Handle tool calls
    if (delta.tool_calls) {
        for (const toolCall of delta.tool_calls) {
            if (toolCall.function?.name) {
                return {
                    type: 'response.function_call.start',
                    function_call: {
                        name: toolCall.function.name,
                        arguments: toolCall.function.arguments || '',
                        id: toolCall.id || ''
                    }
                };
            }
            if (toolCall.function?.arguments) {
                return {
                    type: 'response.function_call.arguments.delta',
                    delta: toolCall.function.arguments
                };
            }
        }
    }
    // Handle completion
    if (finish_reason) {
        return {
            type: 'response.completed',
            metadata: {
                finish_reason,
                usage: chunk.usage
            }
        };
    }
    return null;
}
/**
 * Stream responses from OpenAI completion
 */
async function* streamResponses(completion) {
    try {
        for await (const chunk of completion) {
            const event = chunkToResponseEvent(chunk);
            if (event) {
                yield event;
            }
        }
    }
    catch (error) {
        yield {
            type: 'response.error',
            error: error instanceof Error ? error.message : 'Unknown streaming error'
        };
    }
}
/**
 * Convert response input items to OpenAI messages
 */
function inputItemsToMessages(items) {
    const messages = [];
    for (const item of items) {
        switch (item.role) {
            case 'user':
                messages.push(convertUserMessage(item));
                break;
            case 'assistant':
                messages.push(convertAssistantMessage(item));
                break;
            case 'system':
                messages.push(convertSystemMessage(item));
                break;
            case 'tool':
                messages.push(convertToolMessage(item));
                break;
        }
    }
    return messages;
}
/**
 * Convert user input item to OpenAI message
 */
function convertUserMessage(item) {
    const content = [];
    for (const contentItem of item.content) {
        switch (contentItem.type) {
            case 'input_text':
                if (contentItem.text) {
                    content.push({
                        type: 'text',
                        text: contentItem.text
                    });
                }
                break;
            case 'input_image':
                if (contentItem.image) {
                    content.push({
                        type: 'image_url',
                        image_url: {
                            url: contentItem.image.url || `data:${contentItem.image.mimeType};base64,${contentItem.image.base64}`
                        }
                    });
                }
                break;
        }
    }
    return {
        role: 'user',
        content: content.length === 1 && content[0].type === 'text'
            ? content[0].text
            : content
    };
}
/**
 * Convert assistant input item to OpenAI message
 */
function convertAssistantMessage(item) {
    let content = '';
    const tool_calls = [];
    for (const contentItem of item.content) {
        switch (contentItem.type) {
            case 'input_text':
                if (contentItem.text) {
                    content += contentItem.text;
                }
                break;
            case 'function_call':
                if (contentItem.function_call) {
                    tool_calls.push({
                        id: contentItem.function_call.id,
                        type: 'function',
                        function: {
                            name: contentItem.function_call.name,
                            arguments: contentItem.function_call.arguments
                        }
                    });
                }
                break;
        }
    }
    const message = {
        role: 'assistant'
    };
    if (content) {
        message.content = content;
    }
    if (tool_calls.length > 0) {
        message.tool_calls = tool_calls;
    }
    return message;
}
/**
 * Convert system input item to OpenAI message
 */
function convertSystemMessage(item) {
    const textContent = item.content
        .filter(c => c.type === 'input_text')
        .map(c => c.text)
        .join('\n');
    return {
        role: 'system',
        content: textContent
    };
}
/**
 * Convert tool input item to OpenAI message
 */
function convertToolMessage(item) {
    const functionResult = item.content.find(c => c.type === 'function_result')?.function_result;
    if (!functionResult) {
        throw new Error('Tool message must contain function result');
    }
    return {
        role: 'tool',
        tool_call_id: functionResult.call_id,
        content: functionResult.result
    };
}
/**
 * Create response item from events
 */
function createResponseItemFromEvents(events) {
    let content = '';
    const functionCalls = [];
    let currentFunctionCall = null;
    let metadata = {};
    for (const event of events) {
        switch (event.type) {
            case 'response.output_text.delta':
                if (event.delta) {
                    content += event.delta;
                }
                break;
            case 'response.function_call.start':
                if (event.function_call) {
                    currentFunctionCall = { ...event.function_call };
                }
                break;
            case 'response.function_call.arguments.delta':
                if (currentFunctionCall && event.delta) {
                    currentFunctionCall.arguments = (currentFunctionCall.arguments || '') + event.delta;
                }
                break;
            case 'response.function_call.arguments.done':
                if (currentFunctionCall && currentFunctionCall.name && currentFunctionCall.arguments) {
                    functionCalls.push({
                        name: currentFunctionCall.name,
                        arguments: currentFunctionCall.arguments,
                        id: currentFunctionCall.id || ''
                    });
                    currentFunctionCall = null;
                }
                break;
            case 'response.completed':
                if (event.metadata) {
                    metadata = { ...metadata, ...event.metadata };
                }
                break;
            case 'response.error':
                metadata.error = event.error;
                break;
        }
    }
    // Build response content
    const responseContent = [];
    if (content) {
        responseContent.push({
            type: 'text',
            text: content
        });
    }
    for (const functionCall of functionCalls) {
        responseContent.push({
            type: 'function_call',
            function_call: functionCall
        });
    }
    return {
        id: generateId(),
        type: functionCalls.length > 0 ? 'function_call' : 'message',
        role: 'assistant',
        content: responseContent,
        timestamp: Date.now(),
        metadata
    };
}
/**
 * Create function result response item
 */
function createFunctionResultItem(callId, result, success = true, metadata) {
    return {
        id: generateId(),
        type: 'function_result',
        role: 'tool',
        content: [{
                type: 'function_result',
                function_result: {
                    call_id: callId,
                    result,
                    success,
                    metadata
                }
            }],
        timestamp: Date.now(),
        metadata
    };
}
/**
 * Create error response item
 */
function createErrorItem(error, metadata) {
    return {
        id: generateId(),
        type: 'error',
        role: 'system',
        content: [{
                type: 'text',
                text: `Error: ${error}`
            }],
        timestamp: Date.now(),
        metadata: {
            ...metadata,
            error: true
        }
    };
}
/**
 * Generate unique ID
 */
function generateId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}
/**
 * Calculate token count estimate
 */
function estimateTokenCount(text) {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
}
/**
 * Calculate total token count for conversation
 */
function calculateConversationTokens(items) {
    let totalTokens = 0;
    for (const item of items) {
        for (const content of item.content) {
            if (content.type === 'input_text' && content.text) {
                totalTokens += estimateTokenCount(content.text);
            }
        }
    }
    return totalTokens;
}
/**
 * Truncate conversation to fit within token limit
 */
function truncateConversation(items, maxTokens, preserveSystemMessages = true) {
    const systemMessages = preserveSystemMessages
        ? items.filter(item => item.role === 'system')
        : [];
    const otherMessages = items.filter(item => item.role !== 'system');
    // Calculate tokens for system messages
    const systemTokens = calculateConversationTokens(systemMessages);
    const availableTokens = maxTokens - systemTokens;
    if (availableTokens <= 0) {
        return systemMessages;
    }
    // Add messages from the end until we hit the token limit
    const result = [...systemMessages];
    let currentTokens = systemTokens;
    for (let i = otherMessages.length - 1; i >= 0; i--) {
        const messageTokens = calculateConversationTokens([otherMessages[i]]);
        if (currentTokens + messageTokens <= maxTokens) {
            result.unshift(otherMessages[i]);
            currentTokens += messageTokens;
        }
        else {
            break;
        }
    }
    return result.sort((a, b) => (a.timestamp || 0) - (b.timestamp || 0));
}
//# sourceMappingURL=responses.js.map