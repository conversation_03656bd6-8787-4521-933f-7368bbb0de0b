"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryOverlay = HistoryOverlay;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const ink_1 = require("ink");
function HistoryOverlay({ items, onClose }) {
    const [selectedIndex, setSelectedIndex] = (0, react_1.useState)(0);
    const [showDetails, setShowDetails] = (0, react_1.useState)(false);
    // Filter to show only user messages and important system messages
    const displayItems = items.filter(item => item.role === 'user' ||
        (item.role === 'system' && item.type !== 'function_result') ||
        item.type === 'error');
    (0, ink_1.useInput)((input, key) => {
        if (key.escape) {
            if (showDetails) {
                setShowDetails(false);
            }
            else {
                onClose();
            }
            return;
        }
        if (key.return) {
            setShowDetails(!showDetails);
            return;
        }
        if (key.upArrow && displayItems.length > 0) {
            setSelectedIndex(prev => prev > 0 ? prev - 1 : displayItems.length - 1);
            return;
        }
        if (key.downArrow && displayItems.length > 0) {
            setSelectedIndex(prev => prev < displayItems.length - 1 ? prev + 1 : 0);
            return;
        }
        if (input === 'c') {
            // Clear history (could be implemented)
            return;
        }
    });
    const formatTimestamp = (timestamp) => {
        const date = new Date(timestamp);
        return date.toLocaleString();
    };
    const getItemPreview = (item) => {
        const textContent = item.content
            .filter(c => c.type === 'text')
            .map(c => c.text)
            .join(' ');
        return textContent.length > 80
            ? textContent.substring(0, 80) + '...'
            : textContent;
    };
    const getRoleIcon = (item) => {
        switch (item.role) {
            case 'user':
                return '👤';
            case 'assistant':
                return '🤖';
            case 'system':
                return '⚙️';
            case 'tool':
                return '🔧';
            default:
                return '❓';
        }
    };
    const getRoleColor = (item) => {
        switch (item.role) {
            case 'user':
                return 'cyan';
            case 'assistant':
                return 'green';
            case 'system':
                return 'yellow';
            case 'tool':
                return 'blue';
            default:
                return 'gray';
        }
    };
    if (displayItems.length === 0) {
        return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", padding: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: "\uD83D\uDCDC Conversation History" }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { flex: 1, justifyContent: "center", alignItems: "center", children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "No conversation history yet. Start chatting to see messages here!" }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { marginTop: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Press Esc to close" }) })] }));
    }
    const selectedItem = displayItems[selectedIndex];
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", padding: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "cyan", bold: true, children: ["\uD83D\uDCDC Conversation History (", displayItems.length, " items)"] }) }), showDetails && selectedItem ? (
            /* Detail View */
            (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", flex: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Box, { marginBottom: 2, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: getRoleColor(selectedItem), bold: true, children: [getRoleIcon(selectedItem), " ", selectedItem.role.charAt(0).toUpperCase() + selectedItem.role.slice(1)] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", marginLeft: 2, children: formatTimestamp(selectedItem.timestamp) })] }), (0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", flex: 1, paddingX: 2, borderStyle: "round", children: selectedItem.content.map((content, index) => {
                            switch (content.type) {
                                case 'text':
                                    return ((0, jsx_runtime_1.jsx)(ink_1.Text, { children: content.text }, index));
                                case 'function_call':
                                    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", marginY: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "blue", bold: true, children: ["Function Call: ", content.function_call?.name] }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: content.function_call?.arguments })] }, index));
                                default:
                                    return null;
                            }
                        }) }), selectedItem.metadata && Object.keys(selectedItem.metadata).length > 0 && ((0, jsx_runtime_1.jsxs)(ink_1.Box, { marginTop: 1, paddingX: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", bold: true, children: "Metadata:" }), (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: JSON.stringify(selectedItem.metadata, null, 2) })] }))] })) : (
            /* List View */
            (0, jsx_runtime_1.jsx)(ink_1.Box, { flexDirection: "column", flex: 1, children: displayItems.map((item, index) => ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: index === selectedIndex ? 'cyan' : getRoleColor(item), children: [index === selectedIndex ? '▶ ' : '  ', getRoleIcon(item), " ", formatTimestamp(item.timestamp), " - ", getItemPreview(item)] }) }, item.id))) })), (0, jsx_runtime_1.jsxs)(ink_1.Box, { marginTop: 2, flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: showDetails
                            ? 'Press Enter to go back to list, Esc to close'
                            : 'Use ↑↓ to navigate, Enter for details, Esc to close' }), !showDetails && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", dimColor: true, children: ["Showing ", displayItems.length, " conversation items"] }))] })] }));
}
//# sourceMappingURL=history-overlay.js.map