import { ExecInput, ExecResult, AppConfig } from '../../../types';
import * as rawExec from './raw-exec';
export interface SandboxCapabilities {
    sandboxed: boolean;
    networkRestricted: boolean;
    filesystemRestricted: boolean;
    processRestricted: boolean;
    name: string;
    securityLevel: string;
}
/**
 * Get the best available sandbox implementation for the current platform
 */
export declare function getBestSandbox(): {
    exec: (input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>, signal?: AbortSignal) => Promise<ExecResult>;
    capabilities: SandboxCapabilities;
    available: boolean;
};
/**
 * Execute command with the best available sandbox
 */
export declare function execSandboxed(input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>, signal?: AbortSignal): Promise<ExecResult>;
/**
 * Get current sandbox capabilities
 */
export declare function getSandboxCapabilities(): SandboxCapabilities;
/**
 * Check if any sandbox is available
 */
export declare function isSandboxAvailable(): boolean;
/**
 * Get all available sandbox implementations
 */
export declare function getAvailableSandboxes(): Array<{
    name: string;
    platform: string;
    available: boolean;
    capabilities: SandboxCapabilities;
}>;
/**
 * Test sandbox functionality
 */
export declare function testSandbox(config: AppConfig): Promise<{
    success: boolean;
    sandbox: string;
    capabilities: SandboxCapabilities;
    testResults: Array<{
        test: string;
        passed: boolean;
        details?: string;
    }>;
}>;
/**
 * Get sandbox recommendation based on security requirements
 */
export declare function getSandboxRecommendation(securityLevel: 'low' | 'medium' | 'high'): {
    recommended: string;
    reason: string;
    alternatives: string[];
};
export { rawExec };
export declare const landlock: any;
export declare const macOSSeatbelt: any;
//# sourceMappingURL=index.d.ts.map