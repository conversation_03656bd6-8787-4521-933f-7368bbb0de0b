"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.isAvailable = isAvailable;
exports.execWithSeatbelt = execWithSeatbelt;
exports.getCapabilities = getCapabilities;
exports.getSecurityLevel = getSecurityLevel;
exports.testSeatbelt = testSeatbelt;
exports.getMacOSVersion = getMacOSVersion;
exports.isSIPEnabled = isSIPEnabled;
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const types_1 = require("../../../types");
const platform_commands_1 = require("../platform-commands");
/**
 * macOS Seatbelt sandbox implementation
 * Provides system-level sandboxing using macOS Seatbelt framework
 */
/**
 * Check if Seatbelt is available on the system
 */
function isAvailable() {
    try {
        // Check if we're on macOS
        if (process.platform !== 'darwin') {
            return false;
        }
        // Check if sandbox-exec is available
        return fs.existsSync('/usr/bin/sandbox-exec');
    }
    catch (error) {
        return false;
    }
}
/**
 * Execute command with Seatbelt sandboxing
 */
async function execWithSeatbelt(input, config, additionalWritableRoots = [], abortSignal) {
    if (!isAvailable()) {
        throw new types_1.SecurityError('Seatbelt sandboxing not available on this system');
    }
    const startTime = Date.now();
    const { command, workdir = config.workdir || process.cwd(), timeout = config.timeout || 30000, env = {} } = input;
    // Validate input
    if (!command || command.length === 0) {
        throw new types_1.SecurityError('Empty command not allowed');
    }
    // Adapt command for current platform
    const adaptedCommand = (0, platform_commands_1.adaptCommand)(command);
    // Resolve working directory
    const resolvedWorkdir = path.resolve(workdir);
    // Create Seatbelt profile
    const seatbeltProfile = createSeatbeltProfile(resolvedWorkdir, additionalWritableRoots);
    // Prepare environment
    const execEnv = {
        ...process.env,
        ...env,
        PATH: process.env.PATH || '',
        PWD: resolvedWorkdir
    };
    // Build sandbox-exec command
    const commandString = adaptedCommand.map(arg => `'${arg.replace(/'/g, "'\"'\"'")}'`).join(' ');
    const sandboxArgs = [
        '/usr/bin/sandbox-exec',
        '-p', seatbeltProfile,
        '/bin/bash', '-c', commandString
    ];
    return new Promise((resolve, reject) => {
        let stdout = '';
        let stderr = '';
        let isResolved = false;
        // Create child process
        const child = (0, child_process_1.spawn)(sandboxArgs[0], sandboxArgs.slice(1), {
            cwd: resolvedWorkdir,
            env: execEnv,
            stdio: ['pipe', 'pipe', 'pipe'],
            windowsHide: true
        });
        // Handle abort signal
        const abortHandler = () => {
            if (!isResolved) {
                child.kill('SIGTERM');
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
            }
        };
        if (abortSignal) {
            abortSignal.addEventListener('abort', abortHandler);
        }
        // Set timeout
        const timeoutId = setTimeout(() => {
            if (!isResolved) {
                child.kill('SIGTERM');
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
            }
        }, timeout);
        // Collect stdout
        child.stdout?.on('data', (data) => {
            stdout += data.toString();
        });
        // Collect stderr
        child.stderr?.on('data', (data) => {
            stderr += data.toString();
        });
        // Handle process exit
        child.on('exit', (code, processSignal) => {
            if (isResolved)
                return;
            isResolved = true;
            clearTimeout(timeoutId);
            if (abortSignal) {
                abortSignal.removeEventListener('abort', abortHandler);
            }
            const duration = Date.now() - startTime;
            const exitCode = code || 0;
            const result = {
                success: exitCode === 0,
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode,
                duration,
                command: adaptedCommand,
                workdir: resolvedWorkdir
            };
            resolve(result);
        });
        // Handle process errors
        child.on('error', (error) => {
            if (isResolved)
                return;
            isResolved = true;
            clearTimeout(timeoutId);
            if (abortSignal) {
                abortSignal.removeEventListener('abort', abortHandler);
            }
            const duration = Date.now() - startTime;
            const result = {
                success: false,
                stdout: stdout.trim(),
                stderr: stderr.trim() || error.message,
                exitCode: 1,
                duration,
                command: adaptedCommand,
                workdir: resolvedWorkdir,
                error: error.message
            };
            resolve(result);
        });
    });
}
/**
 * Create a Seatbelt sandbox profile
 */
function createSeatbeltProfile(workdir, additionalWritableRoots) {
    const allowedReadPaths = [
        '/System',
        '/usr',
        '/bin',
        '/sbin',
        '/Library/Frameworks',
        '/Library/PrivateFrameworks',
        '/private/var/db/timezone',
        workdir,
        ...additionalWritableRoots
    ];
    const allowedWritePaths = [
        '/tmp',
        '/private/tmp',
        '/var/tmp',
        workdir,
        ...additionalWritableRoots
    ];
    return `
(version 1)

; Default deny
(deny default)

; Allow basic system operations
(allow process-info*)
(allow process-info-pidinfo)
(allow process-info-pidfdinfo)
(allow process-info-pidfileportinfo)
(allow process-info-setcontrol)
(allow process-info-dirtycontrol)
(allow process-info-rusage)

; Allow reading system files
${allowedReadPaths.map(p => `(allow file-read* (subpath "${p}"))`).join('\n')}

; Allow writing to specific paths
${allowedWritePaths.map(p => `(allow file-write* (subpath "${p}"))`).join('\n')}

; Allow reading standard input/output
(allow file-read-data file-write-data
  (literal "/dev/stdin")
  (literal "/dev/stdout")
  (literal "/dev/stderr")
  (literal "/dev/null")
  (literal "/dev/zero")
  (literal "/dev/urandom"))

; Allow basic networking (if needed)
(allow network-outbound
  (remote tcp "*:80")
  (remote tcp "*:443")
  (remote tcp "*:22"))

; Allow process execution for shell commands
(allow process-exec
  (literal "/bin/bash")
  (literal "/bin/sh")
  (literal "/usr/bin/env")
  (subpath "/usr/bin")
  (subpath "/bin"))

; Allow signal operations
(allow signal (target self))

; Allow memory operations
(allow vm-map)
(allow vm-protect)

; Allow thread operations
(allow thread-create)
(allow thread-set-state)

; Deny dangerous operations
(deny file-write* (subpath "/System"))
(deny file-write* (subpath "/usr"))
(deny file-write* (subpath "/bin"))
(deny file-write* (subpath "/sbin"))
(deny process-exec (subpath "/System/Library/CoreServices"))
(deny network-bind)
(deny network-inbound)
`;
}
/**
 * Get Seatbelt capabilities
 */
function getCapabilities() {
    return {
        sandboxed: true,
        networkRestricted: true,
        filesystemRestricted: true,
        processRestricted: true
    };
}
/**
 * Get security level description
 */
function getSecurityLevel() {
    return 'High - System-level sandboxing with macOS Seatbelt';
}
/**
 * Test Seatbelt functionality
 */
async function testSeatbelt() {
    try {
        if (!isAvailable()) {
            return {
                available: false,
                error: 'Seatbelt not available on this system'
            };
        }
        // Test basic functionality
        const testResult = await execWithSeatbelt({ command: ['echo', 'test'] }, { workdir: process.cwd() });
        return {
            available: testResult.success,
            version: 'macOS Seatbelt',
            features: ['filesystem_restriction', 'network_restriction', 'process_restriction']
        };
    }
    catch (error) {
        return {
            available: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
}
/**
 * Get macOS version information
 */
function getMacOSVersion() {
    try {
        const fs = require('fs');
        const versionPlist = fs.readFileSync('/System/Library/CoreServices/SystemVersion.plist', 'utf8');
        const versionMatch = versionPlist.match(/<key>ProductVersion<\/key>\s*<string>([^<]+)<\/string>/);
        return versionMatch ? versionMatch[1] : null;
    }
    catch (error) {
        return null;
    }
}
/**
 * Check if System Integrity Protection (SIP) is enabled
 */
function isSIPEnabled() {
    try {
        const { execSync } = require('child_process');
        const output = execSync('csrutil status', { encoding: 'utf8' });
        return output.includes('enabled');
    }
    catch (error) {
        // If we can't check, assume it's enabled for safety
        return true;
    }
}
//# sourceMappingURL=macos-seatbelt.js.map