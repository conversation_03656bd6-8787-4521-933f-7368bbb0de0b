import { OpenAI } from 'openai';
import { ResponseEvent, ResponseInputItem, ResponseItem } from '../types';
/**
 * Convert OpenAI chat completion chunk to response event
 */
export declare function chunkToResponseEvent(chunk: OpenAI.ChatCompletionChunk): ResponseEvent | null;
/**
 * Stream responses from OpenAI completion
 */
export declare function streamResponses(completion: AsyncIterable<OpenAI.ChatCompletionChunk>): AsyncGenerator<ResponseEvent>;
/**
 * Convert response input items to OpenAI messages
 */
export declare function inputItemsToMessages(items: ResponseInputItem[]): OpenAI.ChatCompletionMessageParam[];
/**
 * Create response item from events
 */
export declare function createResponseItemFromEvents(events: ResponseEvent[]): ResponseItem;
/**
 * Create function result response item
 */
export declare function createFunctionResultItem(callId: string, result: string, success?: boolean, metadata?: Record<string, any>): ResponseItem;
/**
 * Create error response item
 */
export declare function createErrorItem(error: string, metadata?: Record<string, any>): ResponseItem;
/**
 * Calculate token count estimate
 */
export declare function estimateTokenCount(text: string): number;
/**
 * Calculate total token count for conversation
 */
export declare function calculateConversationTokens(items: ResponseInputItem[]): number;
/**
 * Truncate conversation to fit within token limit
 */
export declare function truncateConversation(items: ResponseInputItem[], maxTokens: number, preserveSystemMessages?: boolean): ResponseInputItem[];
//# sourceMappingURL=responses.d.ts.map