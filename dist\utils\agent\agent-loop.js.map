{"version": 3, "file": "agent-loop.js", "sourceRoot": "", "sources": ["../../../src/utils/agent/agent-loop.ts"], "names": [], "mappings": ";;;AAEA,oDAAiF;AACjF,4CAA8I;AAC9I,+DAA0D;AAC1D,gDAAiD;AAQjD;;GAEG;AACH,MAAa,SAAS;IACZ,KAAK,CAAS;IACd,QAAQ,CAAS;IACjB,GAAG,CAAS;IACZ,MAAM,CAAY;IAClB,UAAU,GAAwB,EAAE,CAAC;IACrC,aAAa,GAAgB,IAAI,GAAG,EAAE,CAAC;IAE/C,YAAY,MAAiB;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,GAAG,GAAG,IAAA,kCAAkB,EAAC;YAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,OAAe,EACf,sBAAsC,EAAE,EACxC,UAA4B,EAAE;QAE9B,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE3D,IAAI,CAAC;YACH,iCAAiC;YACjC,MAAM,SAAS,GAAG,MAAM,IAAA,6BAAe,EAAC,OAAO,CAAC,CAAC;YAEjD,6BAA6B;YAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;YAE9E,6BAA6B;YAC7B,MAAM,QAAQ,GAAG,IAAA,gCAAoB,EAAC,OAAO,CAAC,CAAC;YAE/C,uCAAuC;YACvC,MAAM,aAAa,GAA4C;gBAC7D,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,IAAI,CAAC,eAAe,EAAE;aAChC,CAAC;YAEF,gBAAgB;YAChB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAEvC,4BAA4B;YAC5B,MAAM,gBAAgB,GAAsC;gBAC1D,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,CAAC,aAAa,EAAE,GAAG,QAAQ,CAAC;gBACtC,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBAC3C,WAAW,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAClD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAC3C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBACzC,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,8BAA8B;YAC9B,MAAM,UAAU,GAAG,MAAM,IAAA,yCAAyB,EAAC,IAAI,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;YAE/E,6BAA6B;YAC7B,MAAM,MAAM,GAAG,EAAE,CAAC;YAClB,IAAI,cAAc,GAAG,EAAE,CAAC;YAExB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAA,2BAAe,EAAC,UAAU,CAAC,EAAE,CAAC;gBACtD,IAAI,MAAM,EAAE,OAAO,EAAE,CAAC;oBACpB,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;gBACrC,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEnB,0BAA0B;gBAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,4BAA4B,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAC/D,cAAc,IAAI,KAAK,CAAC,KAAK,CAAC;oBAC9B,UAAU,EAAE,CAAC,cAAc,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,mCAAmC;YACnC,MAAM,YAAY,GAAG,IAAA,wCAA4B,EAAC,MAAM,CAAC,CAAC;YAE1D,mCAAmC;YACnC,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,EAAE,CAAC;gBAC/D,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,kBAAkB,EAAE,MAAM,CAAC,CAAC;YAClF,CAAC;YAED,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;gBAC1D,MAAM,KAAK,CAAC;YACd,CAAC;YAED,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;YAC1C,OAAO,IAAA,2BAAe,EACpB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAClE,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,YAA0B,EAC1B,kBAAmE,EACnE,MAAoB;QAEpB,MAAM,aAAa,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC;QAEnF,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;YACpC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,IAAI,CAAC;oBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAC3C,OAAO,CAAC,aAAa,EACrB,kBAAkB,EAClB,MAAM,CACP,CAAC;oBAEF,kCAAkC;oBAClC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;wBACxB,IAAI,EAAE,iBAAiB;wBACvB,eAAe,EAAE,MAAM;qBACxB,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,mBAAmB;oBACnB,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;wBACxB,IAAI,EAAE,iBAAiB;wBACvB,eAAe,EAAE;4BACf,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,EAAE;4BACjC,MAAM,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;4BAC5E,OAAO,EAAE,KAAK;yBACf;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,YAAiB,EACjB,kBAAmE,EACnE,MAAoB;QAEpB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,YAAY,CAAC;QAEzD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAEpC,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,iBAAiB,EAAE,CAAC;gBACnD,MAAM,MAAM,GAAG,MAAM,IAAA,uCAAiB,EACpC,IAAI,EACJ,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,CAAC,YAAY,EACxB,IAAI,CAAC,MAAM,CAAC,uBAAuB,IAAI,EAAE,EACzC,kBAAkB,EAClB,MAAM,CACP,CAAC;gBAEF,OAAO;oBACL,OAAO,EAAE,EAAE;oBACX,MAAM,EAAE,MAAM,CAAC,UAAU;oBACzB,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC1B,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE;gBAC/F,OAAO,EAAE,KAAK;aACf,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wBAAwB,CAC9B,OAAuB,EACvB,YAA+B;QAE/B,iCAAiC;QACjC,MAAM,YAAY,GAAwB,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7D,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;gBAC5B,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;oBACf,KAAK,MAAM;wBACT,OAAO,EAAE,IAAI,EAAE,YAAqB,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;oBACvD,KAAK,eAAe;wBAClB,OAAO,EAAE,IAAI,EAAE,eAAwB,EAAE,aAAa,EAAE,CAAC,CAAC,aAAa,EAAE,CAAC;oBAC5E,KAAK,iBAAiB;wBACpB,OAAO,EAAE,IAAI,EAAE,iBAA0B,EAAE,eAAe,EAAE,CAAC,CAAC,eAAe,EAAE,CAAC;oBAClF;wBACE,OAAO,EAAE,IAAI,EAAE,YAAqB,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;gBACrD,CAAC;YACH,CAAC,CAAC;YACF,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC,CAAC,CAAC;QAEJ,OAAO,CAAC,GAAG,YAAY,EAAE,YAAY,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,OAAO,sJAAsJ,IAAI,CAAC,MAAM,CAAC,YAAY;;;;;;;;;;;;;;;;6BAgB5J,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE;iBAChD,IAAI,CAAC,MAAM,CAAC,YAAY;;;uFAG8C,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,KAAK,GAAmB,EAAE,CAAC;QAEjC,aAAa;QACb,KAAK,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,mIAAmI;gBAChJ,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE;4BACP,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACzB,WAAW,EAAE,uEAAuE;yBACrF;wBACD,OAAO,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,6EAA6E;yBAC3F;wBACD,OAAO,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,uDAAuD;yBACrE;qBACF;oBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;iBACtB;aACF;SACF,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAA6B;QACxC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;QAE/C,uCAAuC;QACvC,IAAI,SAAS,CAAC,KAAK;YAAE,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAClD,IAAI,SAAS,CAAC,QAAQ;YAAE,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;QAE3D,6CAA6C;QAC7C,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,GAAG,GAAG,IAAA,kCAAkB,EAAC;gBAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;aAC7B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,YAAY;QACV,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK;QACH,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;CACF;AA3TD,8BA2TC"}