#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const jsx_runtime_1 = require("react/jsx-runtime");
const ink_1 = require("ink");
const commander_1 = require("commander");
const app_1 = require("./app");
const config_1 = require("./utils/config");
const types_1 = require("./types");
const openai_client_1 = require("./utils/openai-client");
const sandbox_1 = require("./utils/agent/sandbox");
const program = new commander_1.Command();
program
    .name('kritrima-ai')
    .description('Sophisticated AI-powered command-line interface with multi-provider support')
    .version('1.0.0')
    .option('-m, --model <model>', 'AI model to use')
    .option('-p, --provider <provider>', 'AI provider to use')
    .option('-a, --approval <mode>', 'Approval mode: suggest, auto-edit, full-auto')
    .option('-w, --workdir <path>', 'Working directory')
    .option('-t, --timeout <ms>', 'Command timeout in milliseconds')
    .option('-v, --verbose', 'Verbose output')
    .option('--test-connection', 'Test connection to AI provider')
    .option('--test-sandbox', 'Test sandbox functionality')
    .option('--config', 'Show current configuration')
    .option('--providers', 'List available providers')
    .option('--models [provider]', 'List available models for provider')
    .argument('[message]', 'Initial message to send to AI');
program.parse();
const options = program.opts();
const args = program.args;
async function main() {
    try {
        // Load configuration with command line overrides
        const configOverrides = {};
        if (options.model)
            configOverrides.model = options.model;
        if (options.provider)
            configOverrides.provider = options.provider;
        if (options.approval)
            configOverrides.approvalMode = options.approval;
        if (options.workdir)
            configOverrides.workdir = options.workdir;
        if (options.timeout)
            configOverrides.timeout = parseInt(options.timeout, 10);
        const config = (0, config_1.loadConfig)(configOverrides);
        // Handle special commands
        if (options.config) {
            await showConfig(config);
            return;
        }
        if (options.providers) {
            await showProviders();
            return;
        }
        if (options.models) {
            await showModels(options.models || config.provider);
            return;
        }
        if (options.testConnection) {
            await testConnectionCommand(config);
            return;
        }
        if (options.testSandbox) {
            await testSandboxCommand(config);
            return;
        }
        // Validate API key
        const apiKey = (0, config_1.getApiKey)(config.provider);
        if (!apiKey) {
            console.error(`❌ No API key found for provider: ${config.provider}`);
            console.error(`Please set the appropriate environment variable.`);
            console.error(`For ${config.provider}: ${getProviderEnvKey(config.provider)}`);
            process.exit(1);
        }
        // Test connection if verbose
        if (options.verbose) {
            console.log('🔍 Testing connection...');
            const connected = await (0, openai_client_1.testConnection)(config.provider);
            if (!connected) {
                console.error(`❌ Failed to connect to ${config.provider}`);
                process.exit(1);
            }
            console.log(`✅ Connected to ${config.provider}`);
        }
        // Get initial message from arguments
        const initialMessage = args.join(' ');
        // Render the main application
        const { waitUntilExit } = (0, ink_1.render)((0, jsx_runtime_1.jsx)(app_1.App, { config: config, initialMessage: initialMessage, verbose: options.verbose }));
        await waitUntilExit();
    }
    catch (error) {
        if (error instanceof types_1.ConfigError) {
            console.error(`❌ Configuration Error: ${error.message}`);
        }
        else {
            console.error(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
        process.exit(1);
    }
}
/**
 * Show current configuration
 */
async function showConfig(config) {
    console.log('📋 Current Configuration:');
    console.log(`  Model: ${config.model}`);
    console.log(`  Provider: ${config.provider}`);
    console.log(`  Approval Mode: ${config.approvalMode}`);
    console.log(`  Working Directory: ${config.workdir}`);
    console.log(`  Timeout: ${config.timeout}ms`);
    console.log(`  Max Tokens: ${config.maxTokens}`);
    console.log(`  Temperature: ${config.temperature}`);
    // Show sandbox info
    const sandbox = (0, sandbox_1.getSandboxCapabilities)();
    console.log(`\n🔒 Security:`);
    console.log(`  Sandbox: ${sandbox.name}`);
    console.log(`  Security Level: ${sandbox.securityLevel}`);
    console.log(`  Sandboxed: ${sandbox.sandboxed ? '✅' : '❌'}`);
    console.log(`  Network Restricted: ${sandbox.networkRestricted ? '✅' : '❌'}`);
    console.log(`  Filesystem Restricted: ${sandbox.filesystemRestricted ? '✅' : '❌'}`);
}
/**
 * Show available providers
 */
async function showProviders() {
    const { providers } = await Promise.resolve().then(() => __importStar(require('./utils/providers')));
    console.log('🌐 Available Providers:');
    for (const [key, provider] of Object.entries(providers)) {
        const apiKey = (0, config_1.getApiKey)(key);
        const status = apiKey ? '✅' : '❌';
        console.log(`  ${status} ${provider.name} (${key})`);
        console.log(`     Base URL: ${provider.baseURL}`);
        console.log(`     Env Key: ${provider.envKey}`);
        console.log(`     Default Model: ${provider.defaultModel}`);
        console.log('');
    }
}
/**
 * Show available models for a provider
 */
async function showModels(provider) {
    try {
        const { fetchModels } = await Promise.resolve().then(() => __importStar(require('./utils/model-utils')));
        const { getProviderModels } = await Promise.resolve().then(() => __importStar(require('./utils/providers')));
        console.log(`🤖 Models for ${provider}:`);
        try {
            const models = await fetchModels(provider);
            if (models.length > 0) {
                models.forEach(model => console.log(`  • ${model}`));
            }
            else {
                console.log('  No models found');
            }
        }
        catch (error) {
            console.log('  Failed to fetch from API, showing predefined models:');
            const fallbackModels = getProviderModels(provider);
            fallbackModels.forEach(model => console.log(`  • ${model}`));
        }
    }
    catch (error) {
        console.error(`❌ Error fetching models: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
/**
 * Test connection to AI provider
 */
async function testConnectionCommand(config) {
    console.log(`🔍 Testing connection to ${config.provider}...`);
    try {
        const connected = await (0, openai_client_1.testConnection)(config.provider);
        if (connected) {
            console.log(`✅ Successfully connected to ${config.provider}`);
        }
        else {
            console.log(`❌ Failed to connect to ${config.provider}`);
            process.exit(1);
        }
    }
    catch (error) {
        console.error(`❌ Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        process.exit(1);
    }
}
/**
 * Test sandbox functionality
 */
async function testSandboxCommand(config) {
    console.log('🔒 Testing sandbox functionality...');
    try {
        const { testSandbox } = await Promise.resolve().then(() => __importStar(require('./utils/agent/sandbox')));
        const result = await testSandbox(config);
        console.log(`Sandbox: ${result.sandbox}`);
        console.log(`Security Level: ${result.capabilities.securityLevel}`);
        console.log(`Overall Status: ${result.success ? '✅ PASSED' : '❌ FAILED'}`);
        console.log('\nTest Results:');
        for (const test of result.testResults) {
            const status = test.passed ? '✅' : '❌';
            console.log(`  ${status} ${test.test}: ${test.details || 'OK'}`);
        }
        if (!result.success) {
            process.exit(1);
        }
    }
    catch (error) {
        console.error(`❌ Sandbox test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        process.exit(1);
    }
}
/**
 * Get environment variable key for provider
 */
function getProviderEnvKey(provider) {
    const { getProvider } = require('./utils/providers');
    const providerConfig = getProvider(provider);
    return providerConfig?.envKey || `${provider.toUpperCase()}_API_KEY`;
}
// Handle uncaught errors
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error.message);
    process.exit(1);
});
process.on('unhandledRejection', (reason) => {
    console.error('❌ Unhandled Rejection:', reason);
    process.exit(1);
});
// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Goodbye!');
    process.exit(0);
});
process.on('SIGTERM', () => {
    console.log('\n👋 Goodbye!');
    process.exit(0);
});
// Run the main function
main().catch((error) => {
    console.error('❌ Fatal Error:', error.message);
    process.exit(1);
});
//# sourceMappingURL=cli.js.map