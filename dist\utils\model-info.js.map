{"version": 3, "file": "model-info.js", "sourceRoot": "", "sources": ["../../src/utils/model-info.ts"], "names": [], "mappings": ";;;AAgNA,oCAEC;AAKD,kDAEC;AAKD,sDAIC;AAKD,sDAGC;AAKD,0DAGC;AAkBD,gDAEC;AAKD,oCAYC;AArRD;;GAEG;AACU,QAAA,SAAS,GAA8B;IAClD,gBAAgB;IAChB,OAAO,EAAE;QACP,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,CAAC;QACvD,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;KACvC;IACD,aAAa,EAAE;QACb,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,QAAQ,CAAC;QACjE,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;KACvC;IACD,QAAQ,EAAE;QACR,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,QAAQ;QACd,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,CAAC;QAC1E,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;KACzC;IACD,aAAa,EAAE;QACb,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,QAAQ,CAAC;QACjE,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE;KAC5C;IACD,eAAe,EAAE;QACf,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,eAAe;QACrB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,CAAC;QAC1C,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;KAC1C;IACD,YAAY,EAAE;QACZ,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,yBAAyB,CAAC;QAC9D,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE;KACxC;IACD,SAAS,EAAE;QACT,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC;QACrD,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;KACzC;IAED,uBAAuB;IACvB,YAAY,EAAE;QACZ,EAAE,EAAE,YAAY;QAChB,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,CAAC;QACvD,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;KAC3C;IACD,mBAAmB,EAAE;QACnB,EAAE,EAAE,mBAAmB;QACvB,IAAI,EAAE,mBAAmB;QACzB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,kBAAkB,CAAC;QACpD,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;KAC3C;IACD,gBAAgB,EAAE;QAChB,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,OAAO;QACtB,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,cAAc,CAAC;QACpE,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;KAC3C;IAED,iBAAiB;IACjB,cAAc,EAAE;QACd,EAAE,EAAE,cAAc;QAClB,IAAI,EAAE,cAAc;QACpB,QAAQ,EAAE,SAAS;QACnB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,CAAC;QACtB,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;KAC7C;IACD,eAAe,EAAE;QACf,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,eAAe;QACrB,QAAQ,EAAE,SAAS;QACnB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,CAAC;QAC1C,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;KACzC;IACD,gBAAgB,EAAE;QAChB,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,SAAS;QACnB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,CAAC;QACvD,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;KAC3C;IACD,eAAe,EAAE;QACf,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,eAAe;QACrB,QAAQ,EAAE,SAAS;QACnB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,kBAAkB,EAAE,WAAW,EAAE,cAAc,CAAC;QACvE,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;KACzC;IAED,kBAAkB;IAClB,eAAe,EAAE;QACf,EAAE,EAAE,eAAe;QACnB,IAAI,EAAE,eAAe;QACrB,QAAQ,EAAE,UAAU;QACpB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;QACnC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;KAC7C;IACD,gBAAgB,EAAE;QAChB,EAAE,EAAE,gBAAgB;QACpB,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,UAAU;QACpB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,CAAC;QAC7C,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;KAC7C;IAED,aAAa;IACb,WAAW,EAAE;QACX,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,WAAW;QACjB,QAAQ,EAAE,KAAK;QACf,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,gBAAgB,CAAC;QACrD,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;KACzC;IAED,cAAc;IACd,oBAAoB,EAAE;QACpB,EAAE,EAAE,oBAAoB;QACxB,IAAI,EAAE,cAAc;QACpB,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;QACxC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;KAC7C;IACD,iBAAiB,EAAE;QACjB,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,MAAM;QAChB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC,MAAM,EAAE,gBAAgB,CAAC;QACxC,OAAO,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;KAC3C;IAED,wBAAwB;IACxB,QAAQ,EAAE;QACR,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;QAC/B,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;KACjC;IACD,WAAW,EAAE;QACX,EAAE,EAAE,WAAW;QACf,IAAI,EAAE,YAAY;QAClB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;QACzC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;KACjC;IACD,SAAS,EAAE;QACT,EAAE,EAAE,SAAS;QACb,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;QAC/B,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;KACjC;IACD,aAAa,EAAE;QACb,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,aAAa;QACnB,QAAQ,EAAE,QAAQ;QAClB,aAAa,EAAE,IAAI;QACnB,YAAY,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;QAC/B,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;KACjC;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,YAAY,CAAC,OAAe;IAC1C,OAAO,iBAAS,CAAC,OAAO,CAAC,CAAC;AAC5B,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,QAAgB;IAClD,OAAO,MAAM,CAAC,MAAM,CAAC,iBAAS,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;AAC/E,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,UAAkB;IACtD,OAAO,MAAM,CAAC,MAAM,CAAC,iBAAS,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAC7C,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CACxC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,OAAe;IACnD,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACpC,OAAO,KAAK,EAAE,aAAa,IAAI,IAAI,CAAC,CAAC,mBAAmB;AAC1D,CAAC;AAED;;GAEG;AACH,SAAgB,uBAAuB,CAAC,OAAe,EAAE,UAAkB;IACzE,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACpC,OAAO,KAAK,EAAE,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;AAC3D,CAAC;AAED;;GAEG;AACU,QAAA,oBAAoB,GAAG;IAClC,MAAM,EAAE,CAAC,QAAQ,EAAE,gBAAgB,EAAE,aAAa,EAAE,WAAW,CAAC;IAChE,SAAS,EAAE,CAAC,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,CAAC;IAC/D,MAAM,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,mBAAmB,CAAC;IACtD,IAAI,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;IAC9D,KAAK,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,CAAC;IACxD,MAAM,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,eAAe,CAAC;IACxD,YAAY,EAAE,CAAC,gBAAgB,EAAE,aAAa,EAAE,QAAQ,CAAC;CAC1D,CAAC;AAEF;;GAEG;AACH,SAAgB,kBAAkB,CAAC,OAA0C;IAC3E,OAAO,4BAAoB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC7C,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAC1B,OAAe,EACf,WAAmB,EACnB,YAAoB;IAEpB,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,CAAC;IACpC,IAAI,CAAC,KAAK,EAAE,OAAO;QAAE,OAAO,CAAC,CAAC;IAE9B,MAAM,SAAS,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;IAC7D,MAAM,UAAU,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;IAEhE,OAAO,SAAS,GAAG,UAAU,CAAC;AAChC,CAAC"}