import { ApprovalPolicy, ReviewDecision, ApprovalRequest } from './types';
/**
 * Check if a command can be auto-approved based on approval policy
 */
export declare function canAutoApprove(command: string[], approvalPolicy: ApprovalPolicy, additionalSafeCommands?: string[]): boolean;
/**
 * Assess the risk level of a command
 */
export declare function assessRiskLevel(command: string[]): "low" | "medium" | "high";
/**
 * Create approval request for a command
 */
export declare function createApprovalRequest(command: string[], workdir: string, description?: string): ApprovalRequest;
/**
 * Validate command against security policies
 */
export declare function validateCommand(command: string[], workdir: string, allowedPaths?: string[]): {
    valid: boolean;
    error?: string;
};
/**
 * Format command for display in approval prompt
 */
export declare function formatCommandForDisplay(command: string[], workdir: string): string;
/**
 * Get approval prompt message
 */
export declare function getApprovalPromptMessage(request: ApprovalRequest): string;
/**
 * Get approval options for display
 */
export declare function getApprovalOptions(): Array<{
    key: string;
    label: string;
    description: string;
}>;
/**
 * Parse approval response from user input
 */
export declare function parseApprovalResponse(input: string): ReviewDecision;
/**
 * Get command explanation for help
 */
export declare function getCommandExplanation(command: string[]): string;
/**
 * Check if approval policy allows automatic execution
 */
export declare function shouldRequestApproval(command: string[], approvalPolicy: ApprovalPolicy, additionalSafeCommands?: string[]): boolean;
/**
 * Create security context for command execution
 */
export declare function createSecurityContext(command: string[], workdir: string, approvalPolicy: ApprovalPolicy): {
    allowExecution: boolean;
    requiresApproval: boolean;
    riskLevel: "low" | "medium" | "high";
    restrictions: string[];
};
//# sourceMappingURL=approvals.d.ts.map