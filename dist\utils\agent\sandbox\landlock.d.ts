import { ExecInput, ExecResult, AppConfig } from '../../../types';
/**
 * Linux Landlock LSM (Linux Security Module) sandbox implementation
 * Provides kernel-level sandboxing for file system access
 */
/**
 * Check if Landlock is available on the system
 */
export declare function isAvailable(): boolean;
/**
 * Execute command with Landlock sandboxing
 */
export declare function execWithLandlock(input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>, abortSignal?: AbortSignal): Promise<ExecResult>;
/**
 * Get Landlock capabilities
 */
export declare function getCapabilities(): {
    sandboxed: boolean;
    networkRestricted: boolean;
    filesystemRestricted: boolean;
    processRestricted: boolean;
};
/**
 * Get security level description
 */
export declare function getSecurityLevel(): string;
/**
 * Test Landlock functionality
 */
export declare function testLandlock(): Promise<{
    available: boolean;
    version?: string;
    features?: string[];
    error?: string;
}>;
//# sourceMappingURL=landlock.d.ts.map