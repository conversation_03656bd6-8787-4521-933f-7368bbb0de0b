pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Black Metal (Burzum)
  Author: metalelf0 (https://github.com/metalelf0)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme black-metal-burzum
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #000000  Default Background
base01  #121212  Lighter Background (Used for status bars, line number and folding marks)
base02  #222222  Selection Background
base03  #333333  Comments, Invisibles, Line Highlighting
base04  #999999  Dark Foreground (Used for status bars)
base05  #c1c1c1  Default Foreground, Caret, Delimiters, Operators
base06  #999999  Light Foreground (Not often used)
base07  #c1c1c1  Light Background (Not often used)
base08  #5f8787  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #aaaaaa  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #99bbaa  Classes, Markup Bold, Search Text Background
base0B  #ddeecc  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #aaaaaa  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #888888  Functions, Methods, Attribute IDs, Headings
base0E  #999999  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #444444  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #c1c1c1;
  background: #000000
}
.hljs::selection,
.hljs ::selection {
  background-color: #222222;
  color: #c1c1c1
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #333333 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #333333
}
/* base04 - #999999 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #999999
}
/* base05 - #c1c1c1 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #c1c1c1
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #5f8787
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #aaaaaa
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #99bbaa
}
.hljs-strong {
  font-weight: bold;
  color: #99bbaa
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #ddeecc
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #aaaaaa
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #888888
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #999999
}
.hljs-emphasis {
  color: #999999;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #444444
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}