export interface PatchOperation {
    type: 'create' | 'modify' | 'delete' | 'rename';
    filePath: string;
    newPath?: string;
    content?: string;
    oldContent?: string;
    hunks?: PatchHunk[];
}
export interface PatchHunk {
    oldStart: number;
    oldLines: number;
    newStart: number;
    newLines: number;
    lines: string[];
}
export interface ApplyPatchResult {
    success: boolean;
    filesModified: string[];
    filesCreated: string[];
    filesDeleted: string[];
    conflicts: PatchConflict[];
    backupPaths: string[];
    error?: string;
}
export interface PatchConflict {
    filePath: string;
    line: number;
    expected: string;
    actual: string;
    resolution?: 'skip' | 'force' | 'manual';
}
/**
 * Apply a unified diff patch to files
 */
export declare function applyPatch(patchContent: string, workdir?: string, options?: {
    dryRun?: boolean;
    createBackup?: boolean;
    allowConflicts?: boolean;
    maxFileSize?: number;
}): Promise<ApplyPatchResult>;
/**
 * Parse unified diff format into patch operations
 */
export declare function parsePatch(patchContent: string): PatchOperation[];
/**
 * Parse V4A diff format (custom format for this project)
 */
export declare function parseV4ADiff(diffContent: string): PatchOperation[];
/**
 * Custom error class for patch conflicts
 */
export declare class PatchConflictError extends Error {
    conflicts: PatchConflict[];
    constructor(conflicts: PatchConflict[]);
}
/**
 * Generate a unified diff between two strings
 */
export declare function generateDiff(oldContent: string, newContent: string, filePath?: string): string;
/**
 * Generate a V4A format diff
 */
export declare function generateV4ADiff(operations: PatchOperation[]): string;
/**
 * Clean up backup files older than specified days
 */
export declare function cleanupBackups(workdir: string, olderThanDays?: number): Promise<number>;
//# sourceMappingURL=apply-patch.d.ts.map