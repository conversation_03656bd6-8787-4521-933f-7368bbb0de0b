"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelOverlay = ModelOverlay;
const jsx_runtime_1 = require("react/jsx-runtime");
const react_1 = require("react");
const ink_1 = require("ink");
const providers_1 = require("../../utils/providers");
const model_utils_1 = require("../../utils/model-utils");
function ModelOverlay({ currentModel, currentProvider, onModelChange, onClose }) {
    const [activeTab, setActiveTab] = (0, react_1.useState)('providers');
    const [selectedProviderIndex, setSelectedProviderIndex] = (0, react_1.useState)(0);
    const [selectedModelIndex, setSelectedModelIndex] = (0, react_1.useState)(0);
    const [providers] = (0, react_1.useState)((0, providers_1.getProviderNames)());
    const [models, setModels] = (0, react_1.useState)([]);
    const [loading, setLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    // Initialize selected provider index
    (0, react_1.useEffect)(() => {
        const currentIndex = providers.indexOf(currentProvider);
        if (currentIndex !== -1) {
            setSelectedProviderIndex(currentIndex);
        }
    }, [providers, currentProvider]);
    // Load models when provider changes
    (0, react_1.useEffect)(() => {
        loadModelsForProvider(providers[selectedProviderIndex]);
    }, [selectedProviderIndex, providers]);
    const loadModelsForProvider = async (provider) => {
        setLoading(true);
        setError(null);
        try {
            // Try to fetch models from API
            const fetchedModels = await (0, model_utils_1.fetchModels)(provider);
            setModels(fetchedModels);
            // Set selected model index
            const currentIndex = fetchedModels.indexOf(currentModel);
            setSelectedModelIndex(currentIndex !== -1 ? currentIndex : 0);
        }
        catch (err) {
            // Fallback to predefined models
            const fallbackModels = (0, providers_1.getProviderModels)(provider);
            setModels(fallbackModels);
            setError('Failed to fetch models from API, showing predefined models');
            const currentIndex = fallbackModels.indexOf(currentModel);
            setSelectedModelIndex(currentIndex !== -1 ? currentIndex : 0);
        }
        finally {
            setLoading(false);
        }
    };
    (0, ink_1.useInput)((input, key) => {
        if (key.escape) {
            onClose();
            return;
        }
        if (key.tab) {
            setActiveTab(prev => prev === 'providers' ? 'models' : 'providers');
            return;
        }
        if (key.return) {
            if (activeTab === 'providers') {
                setActiveTab('models');
            }
            else {
                // Select model
                const selectedProvider = providers[selectedProviderIndex];
                const selectedModel = models[selectedModelIndex];
                if (selectedModel) {
                    onModelChange(selectedModel, selectedProvider);
                }
            }
            return;
        }
        if (key.upArrow) {
            if (activeTab === 'providers') {
                setSelectedProviderIndex(prev => prev > 0 ? prev - 1 : providers.length - 1);
            }
            else {
                setSelectedModelIndex(prev => prev > 0 ? prev - 1 : models.length - 1);
            }
            return;
        }
        if (key.downArrow) {
            if (activeTab === 'providers') {
                setSelectedProviderIndex(prev => prev < providers.length - 1 ? prev + 1 : 0);
            }
            else {
                setSelectedModelIndex(prev => prev < models.length - 1 ? prev + 1 : 0);
            }
            return;
        }
    });
    return ((0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", height: "100%", padding: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 2, children: (0, jsx_runtime_1.jsx)(ink_1.Text, { color: "cyan", bold: true, children: "\uD83E\uDD16 Model & Provider Selection" }) }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { marginBottom: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Box, { marginRight: 4, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: activeTab === 'providers' ? 'cyan' : 'gray', bold: true, children: [activeTab === 'providers' ? '▶ ' : '  ', "Providers"] }) }), (0, jsx_runtime_1.jsx)(ink_1.Box, { children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: activeTab === 'models' ? 'cyan' : 'gray', bold: true, children: [activeTab === 'models' ? '▶ ' : '  ', "Models"] }) })] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "row", flex: 1, children: [(0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", width: "50%", marginRight: 2, children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "yellow", bold: true, marginBottom: 1, children: "Providers:" }), providers.map((provider, index) => ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: index === selectedProviderIndex
                                        ? (activeTab === 'providers' ? 'cyan' : 'blue')
                                        : 'gray', children: [index === selectedProviderIndex ? '▶ ' : '  ', provider, provider === currentProvider && ' (current)'] }) }, provider)))] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { flexDirection: "column", width: "50%", children: [(0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "yellow", bold: true, marginBottom: 1, children: ["Models for ", providers[selectedProviderIndex], ":"] }), loading && ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: "blue", children: "Loading models..." })), error && ((0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "red", marginBottom: 1, children: ["\u26A0\uFE0F ", error] })), !loading && models.length === 0 && ((0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "No models available" })), !loading && models.map((model, index) => ((0, jsx_runtime_1.jsx)(ink_1.Box, { marginBottom: 1, children: (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: index === selectedModelIndex
                                        ? (activeTab === 'models' ? 'cyan' : 'blue')
                                        : 'gray', children: [index === selectedModelIndex ? '▶ ' : '  ', model, model === currentModel && providers[selectedProviderIndex] === currentProvider && ' (current)'] }) }, model)))] })] }), (0, jsx_runtime_1.jsxs)(ink_1.Box, { marginTop: 2, flexDirection: "column", children: [(0, jsx_runtime_1.jsx)(ink_1.Text, { color: "gray", children: "Use \u2191\u2193 to navigate, Tab to switch tabs, Enter to select, Esc to close" }), (0, jsx_runtime_1.jsxs)(ink_1.Text, { color: "gray", dimColor: true, children: ["Current: ", currentModel, " (", currentProvider, ")"] })] })] }));
}
//# sourceMappingURL=model-overlay.js.map