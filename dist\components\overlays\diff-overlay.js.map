{"version": 3, "file": "diff-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/diff-overlay.tsx"], "names": [], "mappings": ";;AAmBA,kCA2KC;AA8GD,gDAYC;;AAxTD,iCAAmD;AACnD,6BAA0C;AAkB1C,SAAgB,WAAW,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAoB;IAC9G,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAa,EAAE,CAAC,CAAC;IAC3D,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAC;IACxD,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,IAAA,gBAAQ,EAAuB,SAAS,CAAC,CAAC;IACtF,MAAM,CAAC,UAAU,CAAC,GAAG,IAAA,gBAAQ,EAAC,EAAE,CAAC,CAAC,CAAC,6BAA6B;IAEhE,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAClD,YAAY,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC;IAE7B,IAAA,cAAQ,EAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,cAAc,KAAK,SAAS,EAAE,CAAC;gBACjC,SAAS,EAAE,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,QAAQ,EAAE,CAAC;YACb,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;YACrE,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;YAC7E,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;YACjB,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,EAAE,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC;YACtF,OAAO;QACT,CAAC;QAED,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACnC,SAAS,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACnC,QAAQ,EAAE,CAAC;YACX,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,SAAS,CAAC,KAAK,CAAC,cAAc,EAAE,cAAc,GAAG,UAAU,CAAC,CAAC;IAClF,MAAM,YAAY,GAAG,cAAc,GAAG,CAAC,CAAC;IACxC,MAAM,YAAY,GAAG,cAAc,GAAG,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC;IAEpE,MAAM,YAAY,GAAG,CAAC,IAAc,EAAE,EAAE;QACtC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,OAAO;gBACV,OAAO,OAAO,CAAC;YACjB,KAAK,SAAS;gBACZ,OAAO,KAAK,CAAC;YACf,KAAK,SAAS,CAAC;YACf;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,IAAc,EAAE,EAAE;QACvC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,OAAO;gBACV,OAAO,GAAG,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,GAAG,CAAC;YACb,KAAK,SAAS,CAAC;YACf;gBACE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,iBAAiB,GAAG,CAAC,IAAc,EAAE,EAAE;QAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACvF,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACvF,OAAO,GAAG,MAAM,IAAI,MAAM,EAAE,CAAC;IAC/B,CAAC,CAAC;IAEF,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAE,CAAC,aAElD,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,+CACN,QAAQ,IAClB,GACH,EAGN,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,wBACR,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,MAAM,cAAU,GAAG,EACpE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM,gBAAY,GAAG,EACjE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM,kBAC9C,GACH,EAGL,YAAY,IAAI,CACf,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,2DAA6C,GAC3D,CACP,EAGD,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,IAAI,EAAE,CAAC,EAAE,WAAW,EAAC,OAAO,EAAC,QAAQ,EAAE,CAAC,aACjE,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CACjC,wBAAC,SAAG,eACF,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,kBACxB,iBAAiB,CAAC,IAAI,CAAC,GACnB,EACP,wBAAC,UAAI,IAAC,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,aAC5B,aAAa,CAAC,IAAI,CAAC,OAAG,IAAI,CAAC,OAAO,IAC9B,KANC,cAAc,GAAG,KAAK,CAO1B,CACP,CAAC,EAED,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,CACzB,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,qCAA4B,CAC/C,IACG,EAGL,YAAY,IAAI,CACf,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACf,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,6DAA+C,GAC7D,CACP,EAGD,wBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,EAAE,cAAc,EAAC,QAAQ,aACxC,uBAAC,SAAG,IAAC,WAAW,EAAE,CAAC,YACjB,wBAAC,UAAI,IAAC,KAAK,EAAE,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,mBAC/D,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,iBACtC,GACH,EACN,uBAAC,SAAG,cACF,wBAAC,UAAI,IAAC,KAAK,EAAE,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI,mBAC5D,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,gBACrC,GACH,IACF,EAGN,wBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aACvC,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,4FAEX,EACP,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,sFAEpB,IACH,IACF,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,UAAkB,EAAE,UAAkB;IAC1D,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACxC,MAAM,SAAS,GAAe,EAAE,CAAC;IAEjC,qCAAqC;IACrC,sFAAsF;IACtF,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;IAEjB,OAAO,QAAQ,GAAG,QAAQ,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAChE,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnC,MAAM,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEnC,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YAChC,2BAA2B;YAC3B,SAAS,CAAC,IAAI,CAAC;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,OAAO;gBAChB,aAAa,EAAE,QAAQ,GAAG,CAAC;aAC5B,CAAC,CAAC;YACH,QAAQ,EAAE,CAAC;QACb,CAAC;aAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACvC,2BAA2B;YAC3B,SAAS,CAAC,IAAI,CAAC;gBACb,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,OAAO;gBAChB,aAAa,EAAE,QAAQ,GAAG,CAAC;aAC5B,CAAC,CAAC;YACH,QAAQ,EAAE,CAAC;QACb,CAAC;aAAM,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;YAC/B,qBAAqB;YACrB,SAAS,CAAC,IAAI,CAAC;gBACb,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,OAAO;gBAChB,aAAa,EAAE,QAAQ,GAAG,CAAC;gBAC3B,aAAa,EAAE,QAAQ,GAAG,CAAC;aAC5B,CAAC,CAAC;YACH,QAAQ,EAAE,CAAC;YACX,QAAQ,EAAE,CAAC;QACb,CAAC;aAAM,CAAC;YACN,iEAAiE;YACjE,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,MAAM,SAAS,GAAG,CAAC,CAAC;YAEpB,8CAA8C;YAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,IAAI,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACtE,IAAI,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;oBACvC,uDAAuD;oBACvD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC3B,SAAS,CAAC,IAAI,CAAC;4BACb,IAAI,EAAE,SAAS;4BACf,OAAO,EAAE,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC;4BAC/B,aAAa,EAAE,QAAQ,GAAG,CAAC,GAAG,CAAC;yBAChC,CAAC,CAAC;oBACL,CAAC;oBACD,QAAQ,IAAI,CAAC,CAAC;oBACd,UAAU,GAAG,IAAI,CAAC;oBAClB,MAAM;gBACR,CAAC;YACH,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,8CAA8C;gBAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,SAAS,IAAI,QAAQ,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACtE,IAAI,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;wBACvC,qDAAqD;wBACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;4BAC3B,SAAS,CAAC,IAAI,CAAC;gCACb,IAAI,EAAE,OAAO;gCACb,OAAO,EAAE,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC;gCAC/B,aAAa,EAAE,QAAQ,GAAG,CAAC,GAAG,CAAC;6BAChC,CAAC,CAAC;wBACL,CAAC;wBACD,QAAQ,IAAI,CAAC,CAAC;wBACd,UAAU,GAAG,IAAI,CAAC;wBAClB,MAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,8CAA8C;gBAC9C,SAAS,CAAC,IAAI,CAAC;oBACb,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,OAAO;oBAChB,aAAa,EAAE,QAAQ,GAAG,CAAC;iBAC5B,CAAC,CAAC;gBACH,SAAS,CAAC,IAAI,CAAC;oBACb,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,OAAO;oBAChB,aAAa,EAAE,QAAQ,GAAG,CAAC;iBAC5B,CAAC,CAAC;gBACH,QAAQ,EAAE,CAAC;gBACX,QAAQ,EAAE,CAAC;YACb,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB,CAAC,UAAkB,EAAE,UAAkB;IAKvE,MAAM,IAAI,GAAG,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAElD,OAAO;QACL,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,MAAM;QAC7D,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM;QACjE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,MAAM;KAClE,CAAC;AACJ,CAAC"}