"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.modelRecommendations = exports.modelInfo = void 0;
exports.getModelInfo = getModelInfo;
exports.getModelsByProvider = getModelsByProvider;
exports.getModelsByCapability = getModelsByCapability;
exports.getModelContextLength = getModelContextLength;
exports.modelSupportsCapability = modelSupportsCapability;
exports.getModelsByUseCase = getModelsByUseCase;
exports.estimateCost = estimateCost;
/**
 * Comprehensive model metadata with context lengths and capabilities
 */
exports.modelInfo = {
    // OpenAI Models
    'gpt-4': {
        id: 'gpt-4',
        name: 'GPT-4',
        provider: 'openai',
        contextLength: 8192,
        capabilities: ['text', 'function_calling', 'reasoning'],
        pricing: { input: 0.03, output: 0.06 }
    },
    'gpt-4-turbo': {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        provider: 'openai',
        contextLength: 128000,
        capabilities: ['text', 'function_calling', 'reasoning', 'vision'],
        pricing: { input: 0.01, output: 0.03 }
    },
    'gpt-4o': {
        id: 'gpt-4o',
        name: 'GPT-4o',
        provider: 'openai',
        contextLength: 128000,
        capabilities: ['text', 'function_calling', 'reasoning', 'vision', 'audio'],
        pricing: { input: 0.005, output: 0.015 }
    },
    'gpt-4o-mini': {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        provider: 'openai',
        contextLength: 128000,
        capabilities: ['text', 'function_calling', 'reasoning', 'vision'],
        pricing: { input: 0.00015, output: 0.0006 }
    },
    'gpt-3.5-turbo': {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'openai',
        contextLength: 16385,
        capabilities: ['text', 'function_calling'],
        pricing: { input: 0.0015, output: 0.002 }
    },
    'o1-preview': {
        id: 'o1-preview',
        name: 'o1 Preview',
        provider: 'openai',
        contextLength: 128000,
        capabilities: ['text', 'reasoning', 'complex_problem_solving'],
        pricing: { input: 0.015, output: 0.06 }
    },
    'o1-mini': {
        id: 'o1-mini',
        name: 'o1 Mini',
        provider: 'openai',
        contextLength: 128000,
        capabilities: ['text', 'reasoning', 'math', 'coding'],
        pricing: { input: 0.003, output: 0.012 }
    },
    // Google Gemini Models
    'gemini-pro': {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        provider: 'gemini',
        contextLength: 32768,
        capabilities: ['text', 'function_calling', 'reasoning'],
        pricing: { input: 0.0005, output: 0.0015 }
    },
    'gemini-pro-vision': {
        id: 'gemini-pro-vision',
        name: 'Gemini Pro Vision',
        provider: 'gemini',
        contextLength: 32768,
        capabilities: ['text', 'vision', 'function_calling'],
        pricing: { input: 0.0005, output: 0.0015 }
    },
    'gemini-1.5-pro': {
        id: 'gemini-1.5-pro',
        name: 'Gemini 1.5 Pro',
        provider: 'gemini',
        contextLength: 1048576,
        capabilities: ['text', 'vision', 'function_calling', 'long_context'],
        pricing: { input: 0.0035, output: 0.0105 }
    },
    // Mistral Models
    'mistral-tiny': {
        id: 'mistral-tiny',
        name: 'Mistral Tiny',
        provider: 'mistral',
        contextLength: 32768,
        capabilities: ['text'],
        pricing: { input: 0.00025, output: 0.00025 }
    },
    'mistral-small': {
        id: 'mistral-small',
        name: 'Mistral Small',
        provider: 'mistral',
        contextLength: 32768,
        capabilities: ['text', 'function_calling'],
        pricing: { input: 0.002, output: 0.006 }
    },
    'mistral-medium': {
        id: 'mistral-medium',
        name: 'Mistral Medium',
        provider: 'mistral',
        contextLength: 32768,
        capabilities: ['text', 'function_calling', 'reasoning'],
        pricing: { input: 0.0027, output: 0.0081 }
    },
    'mistral-large': {
        id: 'mistral-large',
        name: 'Mistral Large',
        provider: 'mistral',
        contextLength: 32768,
        capabilities: ['text', 'function_calling', 'reasoning', 'multilingual'],
        pricing: { input: 0.008, output: 0.024 }
    },
    // DeepSeek Models
    'deepseek-chat': {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat',
        provider: 'deepseek',
        contextLength: 32768,
        capabilities: ['text', 'reasoning'],
        pricing: { input: 0.00014, output: 0.00028 }
    },
    'deepseek-coder': {
        id: 'deepseek-coder',
        name: 'DeepSeek Coder',
        provider: 'deepseek',
        contextLength: 32768,
        capabilities: ['text', 'coding', 'reasoning'],
        pricing: { input: 0.00014, output: 0.00028 }
    },
    // xAI Models
    'grok-beta': {
        id: 'grok-beta',
        name: 'Grok Beta',
        provider: 'xai',
        contextLength: 131072,
        capabilities: ['text', 'reasoning', 'real_time_info'],
        pricing: { input: 0.005, output: 0.015 }
    },
    // Groq Models
    'mixtral-8x7b-32768': {
        id: 'mixtral-8x7b-32768',
        name: 'Mixtral 8x7B',
        provider: 'groq',
        contextLength: 32768,
        capabilities: ['text', 'fast_inference'],
        pricing: { input: 0.00027, output: 0.00027 }
    },
    'llama2-70b-4096': {
        id: 'llama2-70b-4096',
        name: 'Llama 2 70B',
        provider: 'groq',
        contextLength: 4096,
        capabilities: ['text', 'fast_inference'],
        pricing: { input: 0.0007, output: 0.0008 }
    },
    // Ollama Models (local)
    'llama2': {
        id: 'llama2',
        name: 'Llama 2',
        provider: 'ollama',
        contextLength: 4096,
        capabilities: ['text', 'local'],
        pricing: { input: 0, output: 0 }
    },
    'codellama': {
        id: 'codellama',
        name: 'Code Llama',
        provider: 'ollama',
        contextLength: 4096,
        capabilities: ['text', 'coding', 'local'],
        pricing: { input: 0, output: 0 }
    },
    'mistral': {
        id: 'mistral',
        name: 'Mistral',
        provider: 'ollama',
        contextLength: 8192,
        capabilities: ['text', 'local'],
        pricing: { input: 0, output: 0 }
    },
    'neural-chat': {
        id: 'neural-chat',
        name: 'Neural Chat',
        provider: 'ollama',
        contextLength: 4096,
        capabilities: ['text', 'local'],
        pricing: { input: 0, output: 0 }
    }
};
/**
 * Get model information by ID
 */
function getModelInfo(modelId) {
    return exports.modelInfo[modelId];
}
/**
 * Get models by provider
 */
function getModelsByProvider(provider) {
    return Object.values(exports.modelInfo).filter(model => model.provider === provider);
}
/**
 * Get models by capability
 */
function getModelsByCapability(capability) {
    return Object.values(exports.modelInfo).filter(model => model.capabilities.includes(capability));
}
/**
 * Get context length for a model
 */
function getModelContextLength(modelId) {
    const model = getModelInfo(modelId);
    return model?.contextLength || 4096; // Default fallback
}
/**
 * Check if model supports a capability
 */
function modelSupportsCapability(modelId, capability) {
    const model = getModelInfo(modelId);
    return model?.capabilities.includes(capability) || false;
}
/**
 * Get recommended models for different use cases
 */
exports.modelRecommendations = {
    coding: ['gpt-4o', 'deepseek-coder', 'gpt-4-turbo', 'codellama'],
    reasoning: ['o1-preview', 'o1-mini', 'gpt-4o', 'mistral-large'],
    vision: ['gpt-4o', 'gpt-4-turbo', 'gemini-pro-vision'],
    fast: ['gpt-4o-mini', 'mixtral-8x7b-32768', 'llama2-70b-4096'],
    local: ['llama2', 'codellama', 'mistral', 'neural-chat'],
    budget: ['gpt-4o-mini', 'mistral-tiny', 'deepseek-chat'],
    long_context: ['gemini-1.5-pro', 'gpt-4-turbo', 'gpt-4o']
};
/**
 * Get models by use case
 */
function getModelsByUseCase(useCase) {
    return exports.modelRecommendations[useCase] || [];
}
/**
 * Calculate estimated cost for a request
 */
function estimateCost(modelId, inputTokens, outputTokens) {
    const model = getModelInfo(modelId);
    if (!model?.pricing)
        return 0;
    const inputCost = (inputTokens / 1000) * model.pricing.input;
    const outputCost = (outputTokens / 1000) * model.pricing.output;
    return inputCost + outputCost;
}
//# sourceMappingURL=model-info.js.map