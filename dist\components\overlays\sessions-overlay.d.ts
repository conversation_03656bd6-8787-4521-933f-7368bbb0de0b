import { Session } from '../../types';
interface SessionsOverlayProps {
    onSessionLoad: (session: Session) => void;
    onSessionSave: (name: string) => void;
    onSessionDelete: (sessionId: string) => void;
    onClose: () => void;
}
export declare function SessionsOverlay({ onSessionLoad, onSessionSave, onSessionDelete, onClose }: SessionsOverlayProps): import("react/jsx-runtime").JSX.Element;
/**
 * Save session to storage
 */
export declare function saveSessionToStorage(session: Session): Promise<void>;
/**
 * Delete session from storage
 */
export declare function deleteSessionFromStorage(sessionId: string): Promise<void>;
export {};
//# sourceMappingURL=sessions-overlay.d.ts.map