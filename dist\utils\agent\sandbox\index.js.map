{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/utils/agent/sandbox/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA,wCAmDC;AAKD,sCAQC;AAKD,wDAEC;AAKD,gDAEC;AAKD,sDAsDC;AAKD,kCAiEC;AAKD,4DA6CC;AAtSD,uCAAyB;AAEzB,oDAAsC;AAuS7B,0BAAO;AArShB,2EAA2E;AAC3E,IAAI,YAAY,GAAQ,IAAI,CAAC;AAC7B,IAAI,iBAAiB,GAAQ,IAAI,CAAC;AAElC,0CAA0C;AAC1C,IAAI,CAAC;IACH,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;QAC9B,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;AACH,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,yBAAyB;AAC3B,CAAC;AAED,IAAI,CAAC;IACH,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE,CAAC;QAC/B,iBAAiB,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAClD,CAAC;AACH,CAAC;AAAC,OAAO,KAAK,EAAE,CAAC;IACf,+BAA+B;AACjC,CAAC;AAWD;;GAEG;AACH,SAAgB,cAAc;IAK5B,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAE/B,wCAAwC;IACxC,IAAI,QAAQ,KAAK,OAAO,IAAI,YAAY,EAAE,WAAW,EAAE,EAAE,CAAC;QACxD,OAAO;YACL,IAAI,EAAE,YAAY,CAAC,gBAAgB;YACnC,YAAY,EAAE;gBACZ,SAAS,EAAE,IAAI;gBACf,iBAAiB,EAAE,IAAI;gBACvB,oBAAoB,EAAE,IAAI;gBAC1B,iBAAiB,EAAE,IAAI;gBACvB,IAAI,EAAE,gBAAgB;gBACtB,aAAa,EAAE,gCAAgC;aAChD;YACD,SAAS,EAAE,IAAI;SAChB,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,KAAK,QAAQ,IAAI,iBAAiB,EAAE,WAAW,EAAE,EAAE,CAAC;QAC9D,OAAO;YACL,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;YACxC,YAAY,EAAE;gBACZ,SAAS,EAAE,IAAI;gBACf,iBAAiB,EAAE,IAAI;gBACvB,oBAAoB,EAAE,IAAI;gBAC1B,iBAAiB,EAAE,IAAI;gBACvB,IAAI,EAAE,gBAAgB;gBACtB,aAAa,EAAE,gCAAgC;aAChD;YACD,SAAS,EAAE,IAAI;SAChB,CAAC;IACJ,CAAC;IAED,gDAAgD;IAChD,OAAO;QACL,IAAI,EAAE,OAAO,CAAC,qBAAqB;QACnC,YAAY,EAAE;YACZ,SAAS,EAAE,KAAK;YAChB,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,KAAK;YAC3B,iBAAiB,EAAE,KAAK;YACxB,IAAI,EAAE,eAAe;YACrB,aAAa,EAAE,8BAA8B;SAC9C;QACD,SAAS,EAAE,IAAI;KAChB,CAAC;AACJ,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,aAAa,CACjC,KAAgB,EAChB,MAAiB,EACjB,0BAAiD,EAAE,EACnD,MAAoB;IAEpB,MAAM,OAAO,GAAG,cAAc,EAAE,CAAC;IACjC,OAAO,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,CAAC,CAAC;AACtE,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB;IACpC,OAAO,cAAc,EAAE,CAAC,YAAY,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,SAAgB,kBAAkB;IAChC,OAAO,cAAc,EAAE,CAAC,SAAS,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB;IAMnC,MAAM,SAAS,GAAG,EAAE,CAAC;IAErB,iBAAiB;IACjB,SAAS,CAAC,IAAI,CAAC;QACb,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,OAAO;QACjB,SAAS,EAAE,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,IAAI,YAAY,EAAE,WAAW,EAAE;QACnE,YAAY,EAAE;YACZ,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,IAAI;YACvB,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,IAAI;YACvB,IAAI,EAAE,gBAAgB;YACtB,aAAa,EAAE,gCAAgC;SAChD;KACF,CAAC,CAAC;IAEH,iBAAiB;IACjB,SAAS,CAAC,IAAI,CAAC;QACb,IAAI,EAAE,gBAAgB;QACtB,QAAQ,EAAE,QAAQ;QAClB,SAAS,EAAE,EAAE,CAAC,QAAQ,EAAE,KAAK,QAAQ,IAAI,iBAAiB,EAAE,WAAW,EAAE;QACzE,YAAY,EAAE;YACZ,SAAS,EAAE,IAAI;YACf,iBAAiB,EAAE,IAAI;YACvB,oBAAoB,EAAE,IAAI;YAC1B,iBAAiB,EAAE,IAAI;YACvB,IAAI,EAAE,gBAAgB;YACtB,aAAa,EAAE,gCAAgC;SAChD;KACF,CAAC,CAAC;IAEH,mCAAmC;IACnC,SAAS,CAAC,IAAI,CAAC;QACb,IAAI,EAAE,eAAe;QACrB,QAAQ,EAAE,KAAK;QACf,SAAS,EAAE,IAAI;QACf,YAAY,EAAE;YACZ,SAAS,EAAE,KAAK;YAChB,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,KAAK;YAC3B,iBAAiB,EAAE,KAAK;YACxB,IAAI,EAAE,eAAe;YACrB,aAAa,EAAE,8BAA8B;SAC9C;KACF,CAAC,CAAC;IAEH,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,WAAW,CAAC,MAAiB;IAMjD,MAAM,OAAO,GAAG,cAAc,EAAE,CAAC;IACjC,MAAM,WAAW,GAAG,EAAE,CAAC;IAEvB,IAAI,CAAC;QACH,kCAAkC;QAClC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,IAAI,CACjC,EAAE,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAC7B,MAAM,CACP,CAAC;QACF,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5D,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM;SACnD,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,IAAI,CAChC,EAAE,OAAO,EAAE,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EACzD,MAAM,CACP,CAAC;QACF,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,0BAA0B;YAChC,MAAM,EAAE,OAAO,CAAC,OAAO;YACvB,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM;SACjD,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,IAAI,CACjC,EAAE,OAAO,EAAE,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EACzD,MAAM,CACP,CAAC;QACF,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,oBAAoB;YAC1B,MAAM,EAAE,QAAQ,CAAC,OAAO;YACxB,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM;SACnD,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE7D,OAAO;YACL,OAAO,EAAE,SAAS;YAClB,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI;YAClC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,WAAW;SACZ,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,wBAAwB;YAC9B,MAAM,EAAE,KAAK;YACb,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAClE,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,IAAI;YAClC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,WAAW;SACZ,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,wBAAwB,CAAC,aAAwC;IAK/E,MAAM,SAAS,GAAG,qBAAqB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAEnE,QAAQ,aAAa,EAAE,CAAC;QACtB,KAAK,MAAM;YACT,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACnE,IAAI,YAAY,EAAE,CAAC;gBACjB,OAAO;oBACL,WAAW,EAAE,YAAY,CAAC,IAAI;oBAC9B,MAAM,EAAE,8DAA8D;oBACtE,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;iBACnF,CAAC;YACJ,CAAC;YACD,MAAM;QAER,KAAK,QAAQ;YACX,6CAA6C;YAC7C,MAAM,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC;YACrF,OAAO;gBACL,WAAW,EAAE,cAAc,CAAC,IAAI;gBAChC,MAAM,EAAE,cAAc,CAAC,YAAY,CAAC,SAAS;oBAC3C,CAAC,CAAC,wCAAwC;oBAC1C,CAAC,CAAC,kDAAkD;gBACtD,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;aACrF,CAAC;QAEJ,KAAK,KAAK,CAAC;QACX;YACE,OAAO;gBACL,WAAW,EAAE,eAAe;gBAC5B,MAAM,EAAE,6CAA6C;gBACrD,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;aACjF,CAAC;IACN,CAAC;IAED,WAAW;IACX,OAAO;QACL,WAAW,EAAE,eAAe;QAC5B,MAAM,EAAE,uBAAuB;QAC/B,YAAY,EAAE,EAAE;KACjB,CAAC;AACJ,CAAC;AAIY,QAAA,QAAQ,GAAG,YAAY,CAAC;AACxB,QAAA,aAAa,GAAG,iBAAiB,CAAC"}