import { OpenAI } from 'openai';
import { AppConfig, ResponseItem, ResponseInputItem, FunctionTool, ApprovalRequest, ResponseFunctionToolCall } from '../../types';
import { createOpenAIClient, createStreamingCompletion } from '../openai-client';
import { inputItemsToMessages, streamResponses, createResponseItemFromEvents, createFunctionResultItem, createErrorItem } from '../responses';
import { handleExecCommand } from './handle-exec-command';
import { createInputItem } from '../input-utils';

export interface AgentLoopOptions {
  onProgress?: (partialResponse: string) => void;
  onApprovalRequired?: (request: ApprovalRequest) => Promise<boolean>;
  signal?: AbortSignal;
}

/**
 * Core agent loop that orchestrates AI interactions with tool calling
 */
export class AgentLoop {
  private model: string;
  private provider: string;
  private oai: OpenAI;
  private config: AppConfig;
  private transcript: ResponseInputItem[] = [];
  private pendingAborts: Set<string> = new Set();

  constructor(config: AppConfig) {
    this.config = config;
    this.model = config.model;
    this.provider = config.provider;
    this.oai = createOpenAIClient({ 
      provider: this.provider,
      timeout: config.timeout 
    });
  }

  /**
   * Process a user message and return AI response
   */
  async processMessage(
    message: string,
    conversationHistory: ResponseItem[] = [],
    options: AgentLoopOptions = {}
  ): Promise<ResponseItem | null> {
    const { onProgress, onApprovalRequired, signal } = options;

    try {
      // Create input item from message
      const inputItem = await createInputItem(message);
      
      // Build conversation context
      const context = this.buildConversationContext(conversationHistory, inputItem);
      
      // Convert to OpenAI messages
      const messages = inputItemsToMessages(context);
      
      // Add system message with instructions
      const systemMessage: OpenAI.ChatCompletionSystemMessageParam = {
        role: 'system',
        content: this.getSystemPrompt()
      };
      
      // Prepare tools
      const tools = this.getAvailableTools();
      
      // Create completion request
      const completionParams: OpenAI.ChatCompletionCreateParams = {
        model: this.model,
        messages: [systemMessage, ...messages],
        tools: tools.length > 0 ? tools : undefined,
        tool_choice: tools.length > 0 ? 'auto' : undefined,
        temperature: this.config.temperature || 0.7,
        max_tokens: this.config.maxTokens || 4096,
        stream: true
      };

      // Create streaming completion
      const completion = await createStreamingCompletion(this.oai, completionParams);
      
      // Process streaming response
      const events = [];
      let currentContent = '';
      
      for await (const event of streamResponses(completion)) {
        if (signal?.aborted) {
          throw new Error('Request aborted');
        }
        
        events.push(event);
        
        // Handle progress updates
        if (event.type === 'response.output_text.delta' && event.delta) {
          currentContent += event.delta;
          onProgress?.(currentContent);
        }
      }
      
      // Create response item from events
      const responseItem = createResponseItemFromEvents(events);
      
      // Handle function calls if present
      if (responseItem.content.some(c => c.type === 'function_call')) {
        return await this.handleFunctionCalls(responseItem, onApprovalRequired, signal);
      }
      
      return responseItem;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw error;
      }
      
      console.error('Agent loop error:', error);
      return createErrorItem(
        error instanceof Error ? error.message : 'Unknown error occurred'
      );
    }
  }

  /**
   * Handle function calls in AI response
   */
  private async handleFunctionCalls(
    responseItem: ResponseItem,
    onApprovalRequired?: (request: ApprovalRequest) => Promise<boolean>,
    signal?: AbortSignal
  ): Promise<ResponseItem> {
    const functionCalls = responseItem.content.filter(c => c.type === 'function_call');
    
    for (const content of functionCalls) {
      if (content.function_call) {
        try {
          const result = await this.executeFunctionCall(
            content.function_call,
            onApprovalRequired,
            signal
          );
          
          // Add function result to response
          responseItem.content.push({
            type: 'function_result',
            function_result: result
          });
        } catch (error) {
          // Add error result
          responseItem.content.push({
            type: 'function_result',
            function_result: {
              call_id: content.function_call.id,
              result: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
              success: false
            }
          });
        }
      }
    }
    
    return responseItem;
  }

  /**
   * Execute a function call
   */
  private async executeFunctionCall(
    functionCall: any,
    onApprovalRequired?: (request: ApprovalRequest) => Promise<boolean>,
    signal?: AbortSignal
  ): Promise<any> {
    const { name, arguments: argsString, id } = functionCall;
    
    try {
      const args = JSON.parse(argsString);
      
      if (name === 'shell' || name === 'execute_command') {
        const result = await handleExecCommand(
          args,
          this.config,
          this.config.approvalMode,
          this.config.additionalWritableRoots || [],
          onApprovalRequired,
          signal
        );
        
        return {
          call_id: id,
          result: result.outputText,
          success: true,
          metadata: result.metadata
        };
      }
      
      throw new Error(`Unknown function: ${name}`);
    } catch (error) {
      return {
        call_id: id,
        result: `Error executing function: ${error instanceof Error ? error.message : 'Unknown error'}`,
        success: false
      };
    }
  }

  /**
   * Build conversation context from history and current input
   */
  private buildConversationContext(
    history: ResponseItem[],
    currentInput: ResponseInputItem
  ): ResponseInputItem[] {
    // Convert history to input items
    const historyItems: ResponseInputItem[] = history.map(item => ({
      role: item.role,
      content: item.content.map(c => {
        switch (c.type) {
          case 'text':
            return { type: 'input_text' as const, text: c.text };
          case 'function_call':
            return { type: 'function_call' as const, function_call: c.function_call };
          case 'function_result':
            return { type: 'function_result' as const, function_result: c.function_result };
          default:
            return { type: 'input_text' as const, text: '' };
        }
      }),
      type: item.type,
      timestamp: item.timestamp
    }));
    
    return [...historyItems, currentInput];
  }

  /**
   * Get system prompt for the AI
   */
  private getSystemPrompt(): string {
    return `You are Kritrima AI, a sophisticated AI assistant with the ability to execute shell commands and interact with the file system. You are running in ${this.config.approvalMode} mode.

Key capabilities:
- Writing, debugging, and explaining code
- Execute shell commands using the shell function
- Read and write and modify files
- Analyze code and provide suggestions
- Help with development tasks

Guidelines:
- Be helpful and accurate in your responses
- When executing commands, explain what you're doing and why
- Be cautious with destructive operations
- Respect the user's approval mode settings
- Provide clear explanations of command outputs

Current working directory: ${this.config.workdir || process.cwd()}
Approval mode: ${this.config.approvalMode}

Available functions:
- shell(command: string[], workdir?: string, timeout?: number): Execute shell commands`;
  }

  /**
   * Get available tools for the AI
   */
  private getAvailableTools(): FunctionTool[] {
    const tools: FunctionTool[] = [];

    // Shell tool
    tools.push({
      type: 'function',
      function: {
        name: 'shell',
        description: 'Execute shell commands and return their output. Use this to run commands, read files, modify files, and interact with the system.',
        parameters: {
          type: 'object',
          properties: {
            command: {
              type: 'array',
              items: { type: 'string' },
              description: 'The command to execute as an array of strings (command and arguments)'
            },
            workdir: {
              type: 'string',
              description: 'Working directory for the command (optional, defaults to current directory)'
            },
            timeout: {
              type: 'number',
              description: 'Timeout in milliseconds (optional, defaults to 30000)'
            }
          },
          required: ['command']
        }
      }
    });

    return tools;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AppConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update model and provider if changed
    if (newConfig.model) this.model = newConfig.model;
    if (newConfig.provider) this.provider = newConfig.provider;
    
    // Recreate OpenAI client if provider changed
    if (newConfig.provider || newConfig.timeout) {
      this.oai = createOpenAIClient({ 
        provider: this.provider,
        timeout: this.config.timeout 
      });
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): AppConfig {
    return { ...this.config };
  }

  /**
   * Clear conversation history
   */
  clearHistory(): void {
    this.transcript = [];
  }

  /**
   * Abort pending operations
   */
  abort(): void {
    // Implementation would cancel ongoing requests
    this.pendingAborts.clear();
  }
}
