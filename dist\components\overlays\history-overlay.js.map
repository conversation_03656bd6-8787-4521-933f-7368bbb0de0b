{"version": 3, "file": "history-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/history-overlay.tsx"], "names": [], "mappings": ";;AASA,wCAgNC;;AAzND,iCAAwC;AACxC,6BAA0C;AAQ1C,SAAgB,cAAc,CAAC,EAAE,KAAK,EAAE,OAAO,EAAuB;IACpE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAC,CAAC,CAAC,CAAC;IACtD,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAEtD,kEAAkE;IAClE,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACvC,IAAI,CAAC,IAAI,KAAK,MAAM;QACpB,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAiB,CAAC;QAC3D,IAAI,CAAC,IAAI,KAAK,OAAO,CACtB,CAAC;IAEF,IAAA,cAAQ,EAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,WAAW,EAAE,CAAC;gBAChB,cAAc,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,cAAc,CAAC,CAAC,WAAW,CAAC,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,gBAAgB,CAAC,IAAI,CAAC,EAAE,CACtB,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAC9C,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7C,gBAAgB,CAAC,IAAI,CAAC,EAAE,CACtB,IAAI,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAC9C,CAAC;YACF,OAAO;QACT,CAAC;QAED,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAClB,uCAAuC;YACvC,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,CAAC,SAAiB,EAAE,EAAE;QAC5C,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IAC/B,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,IAAkB,EAAE,EAAE;QAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO;aAC7B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC;aAC9B,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;aAChB,IAAI,CAAC,GAAG,CAAC,CAAC;QAEb,OAAO,WAAW,CAAC,MAAM,GAAG,EAAE;YAC5B,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;YACtC,CAAC,CAAC,WAAW,CAAC;IAClB,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,CAAC,IAAkB,EAAE,EAAE;QACzC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC;YACd,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC;YACd,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC;YACd,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,GAAG,CAAC;QACf,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,IAAkB,EAAE,EAAE;QAC1C,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB,KAAK,WAAW;gBACd,OAAO,OAAO,CAAC;YACjB,KAAK,QAAQ;gBACX,OAAO,QAAQ,CAAC;YAClB,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAE,CAAC,aAClD,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,wDAEhB,GACH,EAEN,uBAAC,SAAG,IAAC,IAAI,EAAE,CAAC,EAAE,cAAc,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,YACvD,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,kFAEX,GACH,EAEN,uBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,YACf,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,mCAEX,GACH,IACF,CACP,CAAC;IACJ,CAAC;IAED,MAAM,YAAY,GAAG,YAAY,CAAC,aAAa,CAAC,CAAC;IAEjD,OAAO,CACL,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAE,CAAC,aAElD,uBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,YAClB,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,0DACK,YAAY,CAAC,MAAM,eACxC,GACH,EAEL,WAAW,IAAI,YAAY,CAAC,CAAC,CAAC;YAC7B,iBAAiB;YACjB,wBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,IAAI,EAAE,CAAC,aACjC,wBAAC,SAAG,IAAC,YAAY,EAAE,CAAC,aAClB,wBAAC,UAAI,IAAC,KAAK,EAAE,YAAY,CAAC,YAAY,CAAC,EAAE,IAAI,mBAC1C,WAAW,CAAC,YAAY,CAAC,OAAG,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAC9F,EACP,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,UAAU,EAAE,CAAC,YAC7B,eAAe,CAAC,YAAY,CAAC,SAAS,CAAC,GACnC,IACH,EAEN,uBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,OAAO,YAClE,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;4BAC3C,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gCACrB,KAAK,MAAM;oCACT,OAAO,CACL,uBAAC,UAAI,cACF,OAAO,CAAC,IAAI,IADJ,KAAK,CAET,CACR,CAAC;gCAEJ,KAAK,eAAe;oCAClB,OAAO,CACL,wBAAC,SAAG,IAAa,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aAChD,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,sCACL,OAAO,CAAC,aAAa,EAAE,IAAI,IACtC,EACP,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,YACf,OAAO,CAAC,aAAa,EAAE,SAAS,GAC5B,KANC,KAAK,CAOT,CACP,CAAC;gCAEJ;oCACE,OAAO,IAAI,CAAC;4BAChB,CAAC;wBACH,CAAC,CAAC,GACE,EAEL,YAAY,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CACzE,wBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,aAC5B,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,gCAAiB,EACxC,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,YACf,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,GAC1C,IACH,CACP,IACG,CACP,CAAC,CAAC,CAAC;YACF,eAAe;YACf,uBAAC,SAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,IAAI,EAAE,CAAC,YAChC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CACjC,uBAAC,SAAG,IAAe,YAAY,EAAE,CAAC,YAChC,wBAAC,UAAI,IAAC,KAAK,EACT,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,aAEpD,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EACrC,WAAW,CAAC,IAAI,CAAC,OAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,SAAK,cAAc,CAAC,IAAI,CAAC,IACxE,IANC,IAAI,CAAC,EAAE,CAOX,CACP,CAAC,GACE,CACP,EAGD,wBAAC,SAAG,IAAC,SAAS,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,aACvC,uBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,YACf,WAAW;4BACV,CAAC,CAAC,8CAA8C;4BAChD,CAAC,CAAC,qDAAqD,GAEpD,EACN,CAAC,WAAW,IAAI,CACf,wBAAC,UAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,+BAChB,YAAY,CAAC,MAAM,2BACvB,CACR,IACG,IACF,CACP,CAAC;AACJ,CAAC"}