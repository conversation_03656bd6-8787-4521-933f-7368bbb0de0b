@echo off
REM Kritrima AI CLI Deployment Script for Windows
REM This script helps deploy the Kritrima AI CLI for production use

echo 🚀 Kritrima AI CLI Deployment Script
echo ====================================

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo ✅ Node.js detected

REM Install dependencies
echo 📦 Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Run core functionality test
echo 🧪 Testing core functionality...
node test-core.js
if %errorlevel% neq 0 (
    echo ❌ Core functionality test failed
    pause
    exit /b 1
)

REM Create configuration directory
set CONFIG_DIR=%USERPROFILE%\.kritrima-ai
if not exist "%CONFIG_DIR%" (
    echo 📁 Creating configuration directory: %CONFIG_DIR%
    mkdir "%CONFIG_DIR%"
)

REM Create example configuration
set CONFIG_FILE=%CONFIG_DIR%\config.yaml
if not exist "%CONFIG_FILE%" (
    echo ⚙️ Creating example configuration...
    (
        echo # Kritrima AI CLI Configuration
        echo model: gpt-4
        echo provider: openai
        echo approvalMode: dangerous
        echo maxTokens: 4096
        echo temperature: 0.7
        echo timeout: 30000
        echo workdir: %CD%
    ) > "%CONFIG_FILE%"
    echo ✅ Configuration created at: %CONFIG_FILE%
)

REM Create environment file template
if not exist ".env" (
    echo 🔑 Creating environment file template...
    (
        echo # Kritrima AI CLI Environment Variables
        echo # Add your API keys here
        echo.
        echo # OpenAI
        echo OPENAI_API_KEY=your_openai_api_key_here
        echo.
        echo # Anthropic
        echo ANTHROPIC_API_KEY=your_anthropic_api_key_here
        echo.
        echo # Google
        echo GOOGLE_API_KEY=your_google_api_key_here
        echo.
        echo # Mistral
        echo MISTRAL_API_KEY=your_mistral_api_key_here
        echo.
        echo # Custom providers
        echo # CUSTOM_PROVIDER_URL=https://api.example.com
        echo # CUSTOM_PROVIDER_KEY=your_custom_key_here
    ) > ".env"
    echo ✅ Environment template created at: .env
    echo ⚠️  Please edit .env and add your API keys
)

REM Create startup script
echo 📝 Creating startup script...
(
    echo @echo off
    echo REM Kritrima AI CLI Startup Script
    echo.
    echo cd /d "%%~dp0"
    echo.
    echo REM Run the CLI
    echo npx tsx src/cli.tsx %%*
) > "kritrima-ai.bat"

echo ✅ Startup script created: kritrima-ai.bat

REM Test the installation
echo.
echo 🧪 Testing installation...
call kritrima-ai.bat --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Installation test passed
) else (
    echo ⚠️  Installation test failed - you may need to configure API keys
)

echo.
echo 🎉 Deployment Complete!
echo ======================
echo.
echo 📋 Next Steps:
echo 1. Edit .env and add your API keys
echo 2. Run: kritrima-ai.bat --help
echo 3. Start chatting: kritrima-ai.bat
echo.
echo 📖 Documentation:
echo - README.md for detailed usage instructions
echo - plan.md for architecture overview
echo.
echo 🔧 Troubleshooting:
echo - If you see module resolution errors, use: npx tsx src/cli.tsx
echo - For build issues, see README.md for alternative deployment methods
echo.
echo ✨ Features Available:
echo - Multi-provider AI support (OpenAI, Anthropic, Google, Mistral)
echo - Autonomous agent with tool calling
echo - Secure sandbox execution
echo - File patching and modification
echo - Session management
echo - Interactive terminal UI
echo - Approval workflows
echo.
echo 🚀 Ready for production use!
echo.
pause
