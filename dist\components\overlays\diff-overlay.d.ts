interface DiffOverlayProps {
    filePath: string;
    oldContent: string;
    newContent: string;
    onApprove: () => void;
    onReject: () => void;
    onClose: () => void;
}
export declare function DiffOverlay({ filePath, oldContent, newContent, onApprove, onReject, onClose }: DiffOverlayProps): import("react/jsx-runtime").JSX.Element;
/**
 * Calculate diff statistics
 */
export declare function calculateDiffStats(oldContent: string, newContent: string): {
    linesAdded: number;
    linesRemoved: number;
    linesChanged: number;
};
export {};
//# sourceMappingURL=diff-overlay.d.ts.map