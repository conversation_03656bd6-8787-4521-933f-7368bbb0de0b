"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.exec = exec;
exports.execWithBasicSecurity = execWithBasicSecurity;
exports.getExecutionCapabilities = getExecutionCapabilities;
exports.isAvailable = isAvailable;
exports.getSecurityLevel = getSecurityLevel;
const child_process_1 = require("child_process");
const path = __importStar(require("path"));
const types_1 = require("../../../types");
const platform_commands_1 = require("../platform-commands");
/**
 * Execute command without sandboxing (fallback execution)
 * Used when platform-specific sandboxing is unavailable
 */
async function exec(input, config, abortSignal) {
    const startTime = Date.now();
    const { command, workdir = config.workdir || process.cwd(), timeout = config.timeout || 30000, env = {} } = input;
    // Validate input
    if (!command || command.length === 0) {
        throw new types_1.SecurityError('Empty command not allowed');
    }
    // Adapt command for current platform
    const adaptedCommand = (0, platform_commands_1.adaptCommand)(command);
    // Resolve working directory
    const resolvedWorkdir = path.resolve(workdir);
    // Validate working directory exists and is accessible
    try {
        const fs = require('fs');
        if (!fs.existsSync(resolvedWorkdir)) {
            throw new types_1.SecurityError(`Working directory does not exist: ${resolvedWorkdir}`);
        }
        const stats = fs.statSync(resolvedWorkdir);
        if (!stats.isDirectory()) {
            throw new types_1.SecurityError(`Working directory is not a directory: ${resolvedWorkdir}`);
        }
    }
    catch (error) {
        if (error instanceof types_1.SecurityError)
            throw error;
        throw new types_1.SecurityError(`Cannot access working directory: ${resolvedWorkdir}`);
    }
    // Prepare environment
    const execEnv = {
        ...process.env,
        ...env,
        // Ensure PATH is available
        PATH: process.env.PATH || '',
        // Set working directory in environment
        PWD: resolvedWorkdir
    };
    // Get shell and command arguments
    const shell = (0, platform_commands_1.getDefaultShell)();
    const commandString = adaptedCommand.join(' ');
    const shellArgs = (0, platform_commands_1.getShellArgs)(commandString);
    return new Promise((resolve, reject) => {
        let stdout = '';
        let stderr = '';
        let isResolved = false;
        // Create child process
        const child = (0, child_process_1.spawn)(shell, shellArgs, {
            cwd: resolvedWorkdir,
            env: execEnv,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: false, // We're already using shell
            windowsHide: true
        });
        // Handle abort signal
        const abortHandler = () => {
            if (!isResolved) {
                child.kill('SIGTERM');
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
            }
        };
        if (abortSignal) {
            abortSignal.addEventListener('abort', abortHandler);
        }
        // Set timeout
        const timeoutId = setTimeout(() => {
            if (!isResolved) {
                child.kill('SIGTERM');
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
            }
        }, timeout);
        // Collect stdout
        child.stdout?.on('data', (data) => {
            stdout += data.toString();
        });
        // Collect stderr
        child.stderr?.on('data', (data) => {
            stderr += data.toString();
        });
        // Handle process exit
        child.on('exit', (code, processSignal) => {
            if (isResolved)
                return;
            isResolved = true;
            clearTimeout(timeoutId);
            if (abortSignal) {
                abortSignal.removeEventListener('abort', abortHandler);
            }
            const duration = Date.now() - startTime;
            const exitCode = code || 0;
            const result = {
                success: exitCode === 0,
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode,
                duration,
                command: adaptedCommand,
                workdir: resolvedWorkdir
            };
            resolve(result);
        });
        // Handle process errors
        child.on('error', (error) => {
            if (isResolved)
                return;
            isResolved = true;
            clearTimeout(timeoutId);
            if (abortSignal) {
                abortSignal.removeEventListener('abort', abortHandler);
            }
            const duration = Date.now() - startTime;
            const result = {
                success: false,
                stdout: stdout.trim(),
                stderr: stderr.trim() || error.message,
                exitCode: 1,
                duration,
                command: adaptedCommand,
                workdir: resolvedWorkdir,
                error: error.message
            };
            resolve(result);
        });
        // Handle spawn errors
        child.on('spawn', () => {
            // Process spawned successfully
        });
    });
}
/**
 * Execute command with basic security checks
 */
async function execWithBasicSecurity(input, config, additionalWritableRoots = [], abortSignal) {
    // Perform basic security validation
    validateCommandSecurity(input, config, additionalWritableRoots);
    // Execute with raw exec
    return exec(input, config, abortSignal);
}
/**
 * Validate command for basic security
 */
function validateCommandSecurity(input, config, additionalWritableRoots) {
    const { command, workdir = config.workdir || process.cwd() } = input;
    // Check for dangerous command patterns
    const commandString = command.join(' ').toLowerCase();
    // Block obvious dangerous patterns
    const dangerousPatterns = [
        'rm -rf /',
        'del /s /q c:\\',
        'format c:',
        'dd if=/dev/zero',
        'sudo rm',
        ':(){ :|:& };:', // Fork bomb
        'chmod 777',
        'chown root'
    ];
    for (const pattern of dangerousPatterns) {
        if (commandString.includes(pattern)) {
            throw new types_1.SecurityError(`Dangerous command pattern detected: ${pattern}`);
        }
    }
    // Validate working directory is within allowed paths
    const resolvedWorkdir = path.resolve(workdir);
    const allowedRoots = [
        config.workdir || process.cwd(),
        ...additionalWritableRoots
    ].map(root => path.resolve(root));
    const isAllowed = allowedRoots.some(root => resolvedWorkdir.startsWith(root));
    if (!isAllowed) {
        throw new types_1.SecurityError(`Working directory not in allowed paths: ${resolvedWorkdir}`);
    }
    // Check for path traversal in command arguments
    for (const arg of command) {
        if (arg.includes('../') || arg.includes('..\\')) {
            // Allow some safe cases
            if (!isSafePathTraversal(arg, resolvedWorkdir, allowedRoots)) {
                throw new types_1.SecurityError(`Path traversal detected in argument: ${arg}`);
            }
        }
    }
}
/**
 * Check if path traversal is safe (within allowed roots)
 */
function isSafePathTraversal(arg, workdir, allowedRoots) {
    try {
        // Resolve the path relative to workdir
        const resolvedPath = path.resolve(workdir, arg);
        // Check if resolved path is within any allowed root
        return allowedRoots.some(root => resolvedPath.startsWith(root));
    }
    catch (error) {
        // If path resolution fails, consider it unsafe
        return false;
    }
}
/**
 * Get execution capabilities for raw exec
 */
function getExecutionCapabilities() {
    return {
        sandboxed: false,
        networkRestricted: false,
        filesystemRestricted: false,
        processRestricted: false
    };
}
/**
 * Check if raw execution is available
 */
function isAvailable() {
    return true; // Raw execution is always available as fallback
}
/**
 * Get security level description
 */
function getSecurityLevel() {
    return 'Basic security checks only - no sandboxing';
}
//# sourceMappingURL=raw-exec.js.map