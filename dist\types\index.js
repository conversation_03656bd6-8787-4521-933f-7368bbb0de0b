"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationError = exports.SecurityError = exports.ConfigError = exports.NetworkError = exports.KritrimaError = exports.ReviewDecision = void 0;
// Approval system types
var ReviewDecision;
(function (ReviewDecision) {
    ReviewDecision["YES"] = "yes";
    ReviewDecision["NO_CONTINUE"] = "no_continue";
    ReviewDecision["NO_EXIT"] = "no_exit";
    ReviewDecision["ALWAYS"] = "always";
    ReviewDecision["EXPLAIN"] = "explain";
})(ReviewDecision || (exports.ReviewDecision = ReviewDecision = {}));
// Error types
class KritrimaError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'KritrimaError';
    }
}
exports.KritrimaError = KritrimaError;
class NetworkError extends KritrimaError {
    constructor(message, details) {
        super(message, 'NETWORK_ERROR', details);
    }
}
exports.NetworkError = NetworkError;
class ConfigError extends KritrimaError {
    constructor(message, details) {
        super(message, 'CONFIG_ERROR', details);
    }
}
exports.ConfigError = ConfigError;
class SecurityError extends KritrimaError {
    constructor(message, details) {
        super(message, 'SECURITY_ERROR', details);
    }
}
exports.SecurityError = SecurityError;
class ValidationError extends KritrimaError {
    constructor(message, details) {
        super(message, 'VALIDATION_ERROR', details);
    }
}
exports.ValidationError = ValidationError;
//# sourceMappingURL=index.js.map