pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: PaperColor Light
  Author: <PERSON> (http://github.com/j<PERSON><PERSON><PERSON>) based on PaperColor Theme (https://github.com/NLKNguyen/papercolor-theme)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme papercolor-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #eeeeee  Default Background
base01  #af0000  Lighter Background (Used for status bars, line number and folding marks)
base02  #008700  Selection Background
base03  #5f8700  Comments, Invisibles, Line Highlighting
base04  #0087af  Dark Foreground (Used for status bars)
base05  #444444  Default Foreground, Caret, Delimiters, Operators
base06  #005f87  Light Foreground (Not often used)
base07  #878787  Light Background (Not often used)
base08  #bcbcbc  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #d70000  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #d70087  Classes, Markup Bold, Search Text Background
base0B  #8700af  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #d75f00  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #d75f00  Functions, Methods, Attribute IDs, Headings
base0E  #005faf  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #005f87  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #444444;
  background: #eeeeee
}
.hljs::selection,
.hljs ::selection {
  background-color: #008700;
  color: #444444
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #5f8700 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #5f8700
}
/* base04 - #0087af -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #0087af
}
/* base05 - #444444 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #444444
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #bcbcbc
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #d70000
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #d70087
}
.hljs-strong {
  font-weight: bold;
  color: #d70087
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #8700af
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #d75f00
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #d75f00
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #005faf
}
.hljs-emphasis {
  color: #005faf;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #005f87
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}