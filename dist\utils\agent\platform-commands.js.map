{"version": 3, "file": "platform-commands.js", "sourceRoot": "", "sources": ["../../../src/utils/agent/platform-commands.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA,gDAEC;AAKD,oCAqBC;AA8TD,0CAQC;AAKD,oCAQC;AAzcD,uCAAyB;AAGzB;;GAEG;AACH,MAAM,wBAAwB,GAA2B;IACvD,IAAI,EAAE,KAAK;IACX,KAAK,EAAE,MAAM;IACb,MAAM,EAAE,SAAS;IACjB,IAAI,EAAE,KAAK;IACX,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE,UAAU;IAChB,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,KAAK;IACd,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,MAAM;IACd,OAAO,EAAE,OAAO;IAChB,OAAO,EAAE,QAAQ;IACjB,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE,QAAQ;IACd,MAAM,EAAE,WAAW;IACnB,IAAI,EAAE,eAAe;CACtB,CAAC;AAEF;;GAEG;AACH,MAAM,wBAAwB,GAA2B;IACvD,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,KAAK;IACb,SAAS,EAAE,MAAM;IACjB,KAAK,EAAE,IAAI;IACX,MAAM,EAAE,IAAI;IACZ,MAAM,EAAE,IAAI;IACZ,IAAI,EAAE,OAAO;IACb,IAAI,EAAE,OAAO;IACb,OAAO,EAAE,OAAO;IAChB,UAAU,EAAE,IAAI;IAChB,UAAU,EAAE,MAAM;IAClB,KAAK,EAAE,OAAO;IACd,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,OAAO;CAClB,CAAC;AAEF;;GAEG;AACH,MAAM,iBAAiB,GAA6C;IAClE,IAAI,EAAE;QACJ,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QACnB,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;KACpB;IACD,KAAK,EAAE;QACL,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;KACb;IACD,MAAM,EAAE;QACN,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;KACb;IACD,SAAS,EAAE;QACT,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;KACb;IACD,IAAI,EAAE;QACJ,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;QAClB,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;KAC1B;IACD,KAAK,EAAE;QACL,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;QACZ,IAAI,EAAE,CAAC,IAAI,CAAC;KACb;CACF,CAAC;AAEF;;GAEG;AACH,SAAgB,kBAAkB;IAChC,OAAO,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC;AACxD,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,OAAiB;IAC5C,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,OAAO,CAAC;IAEzC,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;IACtC,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;IAC/B,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAEnC,gDAAgD;IAChD,IAAI,QAAQ,KAAK,SAAS,IAAI,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC;QACzD,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,IAAI,QAAQ,KAAK,MAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;QACnD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,oCAAoC;IACpC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;SAAM,CAAC;QACN,OAAO,WAAW,CAAC,OAAO,CAAC,CAAC;IAC9B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,OAAiB;IACvC,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;IAC/B,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAEnC,yBAAyB;IACzB,MAAM,UAAU,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IACtD,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,gDAAgD;QAChD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,uBAAuB;IACvB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,IAAI;YACP,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;QAE5B,KAAK,KAAK;YACR,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;QAE9B,KAAK,MAAM;YACT,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAElC,KAAK,IAAI;YACP,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;QAE5B,KAAK,IAAI;YACP,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;QAE7B,KAAK,IAAI;YACP,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;QAE7B,KAAK,OAAO;YACV,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAEhC,KAAK,KAAK;YACR,OAAO,CAAC,IAAI,CAAC,CAAC;QAEhB,KAAK,OAAO;YACV,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;QAE5B;YACE,OAAO,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,CAAC;IACjC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,OAAiB;IACpC,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;IAC/B,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAEnC,sBAAsB;IACtB,MAAM,OAAO,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IACnD,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,uBAAuB;IACvB,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,KAAK;YACR,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;QAE5B,KAAK,MAAM;YACT,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;QAE9B,KAAK,SAAS;YACZ,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAElC,KAAK,KAAK;YACR,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;QAE5B,KAAK,MAAM;YACT,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;QAE7B,KAAK,MAAM;YACT,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;QAE7B;YACE,OAAO,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9B,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,IAAc;IAClC,MAAM,OAAO,GAAa,CAAC,KAAK,CAAC,CAAC;IAElC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,uDAAuD;YACvD,SAAS;QACX,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,qBAAqB;YACrB,SAAS;QACX,CAAC;aAAM,CAAC;YACN,gBAAgB;YAChB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,IAAc;IACpC,iCAAiC;IACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1D,OAAO,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC;AAC/B,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,IAAc;IACxC,MAAM,WAAW,GAAa,CAAC,SAAS,CAAC,CAAC;IAC1C,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,KAAK,GAAa,EAAE,CAAC;IAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAEpB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC;aAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,GAAG,GAAG,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAED,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;IAC3B,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,IAAc;IAClC,MAAM,OAAO,GAAa,CAAC,KAAK,CAAC,CAAC;IAElC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC;YAC1C,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;aAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,IAAc;IACnC,6CAA6C;IAC7C,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1D,OAAO,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC;AAC/B,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,IAAc;IACnC,MAAM,QAAQ,GAAa,CAAC,MAAM,CAAC,CAAC;IAEpC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjC,qCAAqC;YACrC,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;YACtB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAc;IACtC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IAExC,+BAA+B;IAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;AAClC,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,IAAc;IAClC,MAAM,MAAM,GAAa,CAAC,IAAI,CAAC,CAAC;IAEhC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,IAAc;IACpC,OAAO,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;AAC1B,CAAC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,IAAc;IACxC,MAAM,QAAQ,GAAa,CAAC,MAAM,CAAC,CAAC;IAEpC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,YAAY,CAAC,IAAc;IAClC,MAAM,MAAM,GAAa,CAAC,IAAI,CAAC,CAAC;IAEhC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,IAAc;IACnC,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,IAAc;IACnC,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,GAAW;IACnC,OAAO,MAAM,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;QACrD,CAAC,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9F,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,GAAW;IAChC,OAAO,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;QACnD,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACpF,CAAC;AAED;;GAEG;AACH,SAAgB,eAAe;IAC7B,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;IAEtC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC;IAC1C,CAAC;SAAM,CAAC;QACN,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,WAAW,CAAC;IAC1C,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,OAAe;IAC1C,MAAM,QAAQ,GAAG,kBAAkB,EAAE,CAAC;IAEtC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAC3B,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACzB,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACzB,CAAC;AACH,CAAC"}