import { ExecInput, ExecResult, AppConfig } from '../../../types';
/**
 * Execute command without sandboxing (fallback execution)
 * Used when platform-specific sandboxing is unavailable
 */
export declare function exec(input: ExecInput, config: AppConfig, abortSignal?: AbortSignal): Promise<ExecResult>;
/**
 * Execute command with basic security checks
 */
export declare function execWithBasicSecurity(input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>, abortSignal?: AbortSignal): Promise<ExecResult>;
/**
 * Get execution capabilities for raw exec
 */
export declare function getExecutionCapabilities(): {
    sandboxed: boolean;
    networkRestricted: boolean;
    filesystemRestricted: boolean;
    processRestricted: boolean;
};
/**
 * Check if raw execution is available
 */
export declare function isAvailable(): boolean;
/**
 * Get security level description
 */
export declare function getSecurityLevel(): string;
//# sourceMappingURL=raw-exec.d.ts.map