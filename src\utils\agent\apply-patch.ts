import * as fs from 'fs-extra';
import * as path from 'path';
import * as diff from 'diff';
import { ExecResult, ValidationError, SecurityError } from '../../types';

export interface PatchOperation {
  type: 'create' | 'modify' | 'delete' | 'rename';
  filePath: string;
  newPath?: string;
  content?: string;
  oldContent?: string;
  hunks?: PatchHunk[];
}

export interface PatchHunk {
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
  lines: string[];
}

export interface ApplyPatchResult {
  success: boolean;
  filesModified: string[];
  filesCreated: string[];
  filesDeleted: string[];
  conflicts: PatchConflict[];
  backupPaths: string[];
  error?: string;
}

export interface PatchConflict {
  filePath: string;
  line: number;
  expected: string;
  actual: string;
  resolution?: 'skip' | 'force' | 'manual';
}

/**
 * Apply a unified diff patch to files
 */
export async function applyPatch(
  patchContent: string,
  workdir: string = process.cwd(),
  options: {
    dryRun?: boolean;
    createBackup?: boolean;
    allowConflicts?: boolean;
    maxFileSize?: number;
  } = {}
): Promise<ApplyPatchResult> {
  const {
    dryRun = false,
    createBackup = true,
    allowConflicts = false,
    maxFileSize = 10 * 1024 * 1024 // 10MB
  } = options;

  const result: ApplyPatchResult = {
    success: false,
    filesModified: [],
    filesCreated: [],
    filesDeleted: [],
    conflicts: [],
    backupPaths: []
  };

  try {
    // Parse patch content
    const operations = parsePatch(patchContent);
    
    // Validate operations
    await validatePatchOperations(operations, workdir, maxFileSize);

    // Create backups if requested
    if (createBackup && !dryRun) {
      result.backupPaths = await createBackups(operations, workdir);
    }

    // Apply each operation
    for (const operation of operations) {
      try {
        await applyPatchOperation(operation, workdir, dryRun);
        
        switch (operation.type) {
          case 'create':
            result.filesCreated.push(operation.filePath);
            break;
          case 'modify':
            result.filesModified.push(operation.filePath);
            break;
          case 'delete':
            result.filesDeleted.push(operation.filePath);
            break;
          case 'rename':
            result.filesModified.push(operation.filePath);
            if (operation.newPath) {
              result.filesCreated.push(operation.newPath);
            }
            break;
        }
      } catch (error) {
        if (error instanceof PatchConflictError) {
          result.conflicts.push(...error.conflicts);
          if (!allowConflicts) {
            throw error;
          }
        } else {
          throw error;
        }
      }
    }

    result.success = result.conflicts.length === 0 || allowConflicts;
    return result;
  } catch (error) {
    result.error = error instanceof Error ? error.message : 'Unknown error';
    
    // Restore from backups if operation failed
    if (!dryRun && result.backupPaths.length > 0) {
      await restoreFromBackups(result.backupPaths, workdir);
    }
    
    return result;
  }
}

/**
 * Parse unified diff format into patch operations
 */
export function parsePatch(patchContent: string): PatchOperation[] {
  const operations: PatchOperation[] = [];
  const lines = patchContent.split('\n');
  let currentOperation: Partial<PatchOperation> | null = null;
  let currentHunk: Partial<PatchHunk> | null = null;
  let hunkLines: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // File header patterns
    if (line.startsWith('--- ')) {
      // Start of new file operation
      if (currentOperation) {
        if (currentHunk) {
          currentHunk.lines = hunkLines;
          currentOperation.hunks = currentOperation.hunks || [];
          currentOperation.hunks.push(currentHunk as PatchHunk);
        }
        operations.push(currentOperation as PatchOperation);
      }

      currentOperation = {
        hunks: []
      };
      currentHunk = null;
      hunkLines = [];

      // Extract source file path
      const sourcePath = line.substring(4).trim();
      if (sourcePath !== '/dev/null') {
        currentOperation.filePath = sourcePath.replace(/^[ab]\//, '');
      }
    } else if (line.startsWith('+++ ')) {
      // Target file path
      if (currentOperation) {
        const targetPath = line.substring(4).trim();
        if (targetPath !== '/dev/null') {
          if (!currentOperation.filePath) {
            currentOperation.filePath = targetPath.replace(/^[ab]\//, '');
            currentOperation.type = 'create';
          } else if (currentOperation.filePath !== targetPath.replace(/^[ab]\//, '')) {
            currentOperation.newPath = targetPath.replace(/^[ab]\//, '');
            currentOperation.type = 'rename';
          } else {
            currentOperation.type = 'modify';
          }
        } else {
          currentOperation.type = 'delete';
        }
      }
    } else if (line.startsWith('@@')) {
      // Hunk header
      if (currentHunk) {
        currentHunk.lines = hunkLines;
        currentOperation!.hunks!.push(currentHunk as PatchHunk);
      }

      const hunkMatch = line.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/);
      if (hunkMatch) {
        currentHunk = {
          oldStart: parseInt(hunkMatch[1]),
          oldLines: parseInt(hunkMatch[2] || '1'),
          newStart: parseInt(hunkMatch[3]),
          newLines: parseInt(hunkMatch[4] || '1'),
          lines: []
        };
        hunkLines = [];
      }
    } else if (currentHunk && (line.startsWith(' ') || line.startsWith('+') || line.startsWith('-'))) {
      // Hunk content
      hunkLines.push(line);
    }
  }

  // Add final operation
  if (currentOperation) {
    if (currentHunk) {
      currentHunk.lines = hunkLines;
      currentOperation.hunks = currentOperation.hunks || [];
      currentOperation.hunks.push(currentHunk as PatchHunk);
    }
    operations.push(currentOperation as PatchOperation);
  }

  return operations;
}

/**
 * Parse V4A diff format (custom format for this project)
 */
export function parseV4ADiff(diffContent: string): PatchOperation[] {
  const operations: PatchOperation[] = [];
  const sections = diffContent.split(/\*\*\* \[(\w+)\] File: (.+)/);

  for (let i = 1; i < sections.length; i += 3) {
    const action = sections[i].toUpperCase();
    const filePath = sections[i + 1].trim();
    const content = sections[i + 2];

    switch (action) {
      case 'CREATE':
        operations.push({
          type: 'create',
          filePath,
          content: content.trim()
        });
        break;
      
      case 'MODIFY':
        operations.push({
          type: 'modify',
          filePath,
          content: content.trim()
        });
        break;
      
      case 'DELETE':
        operations.push({
          type: 'delete',
          filePath
        });
        break;
    }
  }

  return operations;
}

/**
 * Custom error class for patch conflicts
 */
export class PatchConflictError extends Error {
  constructor(public conflicts: PatchConflict[]) {
    super(`Patch conflicts detected in ${conflicts.length} locations`);
    this.name = 'PatchConflictError';
  }
}

/**
 * Validate patch operations before applying
 */
async function validatePatchOperations(
  operations: PatchOperation[],
  workdir: string,
  maxFileSize: number
): Promise<void> {
  for (const operation of operations) {
    const fullPath = path.resolve(workdir, operation.filePath);

    // Security check: ensure path is within workdir
    if (!fullPath.startsWith(path.resolve(workdir))) {
      throw new SecurityError(`Path traversal detected: ${operation.filePath}`);
    }

    // Check file size limits
    if (operation.content && Buffer.byteLength(operation.content, 'utf8') > maxFileSize) {
      throw new ValidationError(`File too large: ${operation.filePath} (max: ${maxFileSize} bytes)`);
    }

    // Validate file operations
    switch (operation.type) {
      case 'create':
        if (fs.existsSync(fullPath)) {
          throw new ValidationError(`File already exists: ${operation.filePath}`);
        }
        break;

      case 'modify':
      case 'delete':
        if (!fs.existsSync(fullPath)) {
          throw new ValidationError(`File not found: ${operation.filePath}`);
        }
        break;

      case 'rename':
        if (!fs.existsSync(fullPath)) {
          throw new ValidationError(`Source file not found: ${operation.filePath}`);
        }
        if (operation.newPath && fs.existsSync(path.resolve(workdir, operation.newPath))) {
          throw new ValidationError(`Target file already exists: ${operation.newPath}`);
        }
        break;
    }
  }
}

/**
 * Apply a single patch operation
 */
async function applyPatchOperation(
  operation: PatchOperation,
  workdir: string,
  dryRun: boolean
): Promise<void> {
  const fullPath = path.resolve(workdir, operation.filePath);

  switch (operation.type) {
    case 'create':
      if (!dryRun) {
        await fs.ensureDir(path.dirname(fullPath));
        await fs.writeFile(fullPath, operation.content || '');
      }
      break;

    case 'delete':
      if (!dryRun) {
        await fs.remove(fullPath);
      }
      break;

    case 'modify':
      if (operation.hunks && operation.hunks.length > 0) {
        await applyHunks(fullPath, operation.hunks, dryRun);
      } else if (operation.content !== undefined) {
        if (!dryRun) {
          await fs.writeFile(fullPath, operation.content);
        }
      }
      break;

    case 'rename':
      if (operation.newPath) {
        const newFullPath = path.resolve(workdir, operation.newPath);
        if (!dryRun) {
          await fs.ensureDir(path.dirname(newFullPath));
          await fs.move(fullPath, newFullPath);
        }
      }
      break;
  }
}

/**
 * Apply hunks to a file
 */
async function applyHunks(
  filePath: string,
  hunks: PatchHunk[],
  dryRun: boolean
): Promise<void> {
  const originalContent = await fs.readFile(filePath, 'utf8');
  const originalLines = originalContent.split('\n');
  const newLines: string[] = [];
  const conflicts: PatchConflict[] = [];

  let originalIndex = 0;

  for (const hunk of hunks) {
    // Copy lines before the hunk
    while (originalIndex < hunk.oldStart - 1) {
      newLines.push(originalLines[originalIndex]);
      originalIndex++;
    }

    // Apply hunk changes
    let hunkOriginalIndex = 0;
    let hunkNewIndex = 0;

    for (const line of hunk.lines) {
      const operation = line[0];
      const content = line.slice(1);

      switch (operation) {
        case ' ': // Context line
          if (originalIndex < originalLines.length) {
            if (originalLines[originalIndex] !== content) {
              conflicts.push({
                filePath,
                line: originalIndex + 1,
                expected: content,
                actual: originalLines[originalIndex]
              });
            }
            newLines.push(originalLines[originalIndex]);
            originalIndex++;
          }
          hunkOriginalIndex++;
          hunkNewIndex++;
          break;

        case '-': // Deleted line
          if (originalIndex < originalLines.length) {
            if (originalLines[originalIndex] !== content) {
              conflicts.push({
                filePath,
                line: originalIndex + 1,
                expected: content,
                actual: originalLines[originalIndex]
              });
            }
            originalIndex++;
          }
          hunkOriginalIndex++;
          break;

        case '+': // Added line
          newLines.push(content);
          hunkNewIndex++;
          break;
      }
    }
  }

  // Copy remaining lines
  while (originalIndex < originalLines.length) {
    newLines.push(originalLines[originalIndex]);
    originalIndex++;
  }

  if (conflicts.length > 0) {
    throw new PatchConflictError(conflicts);
  }

  if (!dryRun) {
    await fs.writeFile(filePath, newLines.join('\n'));
  }
}

/**
 * Create backups of files that will be modified
 */
async function createBackups(
  operations: PatchOperation[],
  workdir: string
): Promise<string[]> {
  const backupPaths: string[] = [];
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

  for (const operation of operations) {
    if (operation.type === 'modify' || operation.type === 'delete' || operation.type === 'rename') {
      const fullPath = path.resolve(workdir, operation.filePath);

      if (fs.existsSync(fullPath)) {
        const backupPath = `${fullPath}.backup-${timestamp}`;
        await fs.copy(fullPath, backupPath);
        backupPaths.push(backupPath);
      }
    }
  }

  return backupPaths;
}

/**
 * Restore files from backups
 */
async function restoreFromBackups(
  backupPaths: string[],
  workdir: string
): Promise<void> {
  for (const backupPath of backupPaths) {
    const originalPath = backupPath.replace(/\.backup-[^.]+$/, '');

    try {
      if (fs.existsSync(backupPath)) {
        await fs.copy(backupPath, originalPath);
        await fs.remove(backupPath);
      }
    } catch (error) {
      console.warn(`Failed to restore backup ${backupPath}:`, error);
    }
  }
}

/**
 * Generate a unified diff between two strings
 */
export function generateDiff(
  oldContent: string,
  newContent: string,
  filePath: string = 'file'
): string {
  const patches = diff.createPatch(filePath, oldContent, newContent);
  return patches;
}

/**
 * Generate a V4A format diff
 */
export function generateV4ADiff(
  operations: PatchOperation[]
): string {
  const sections: string[] = [];

  for (const operation of operations) {
    switch (operation.type) {
      case 'create':
        sections.push(`*** [CREATE] File: ${operation.filePath}`);
        sections.push(operation.content || '');
        break;

      case 'modify':
        sections.push(`*** [MODIFY] File: ${operation.filePath}`);
        sections.push(operation.content || '');
        break;

      case 'delete':
        sections.push(`*** [DELETE] File: ${operation.filePath}`);
        break;
    }
  }

  return sections.join('\n\n');
}

/**
 * Clean up backup files older than specified days
 */
export async function cleanupBackups(
  workdir: string,
  olderThanDays: number = 7
): Promise<number> {
  const cutoffTime = Date.now() - (olderThanDays * 24 * 60 * 60 * 1000);
  let cleanedCount = 0;

  async function cleanupDirectory(dir: string): Promise<void> {
    const entries = await fs.readdir(dir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.isDirectory()) {
        await cleanupDirectory(fullPath);
      } else if (entry.name.includes('.backup-')) {
        const stats = await fs.stat(fullPath);
        if (stats.mtime.getTime() < cutoffTime) {
          await fs.remove(fullPath);
          cleanedCount++;
        }
      }
    }
  }

  await cleanupDirectory(workdir);
  return cleanedCount;
}
